/**
 * Enhanced Bank Reconciliation Tool
 * Provides improved UI, filtering, and matching capabilities
 */

frappe.provide("csf_tz.bank_reconciliation");

csf_tz.bank_reconciliation.EnhancedDataTableManager = class extends erpnext.accounts.bank_reconciliation.DataTableManager {
    constructor(opts) {
        super(opts);
        this.filters = {};
        this.saved_filters = [];
        this.active_filter_name = null;
        this.init_enhanced_features();
    }

    init_enhanced_features() {
        this.load_saved_filters();
        this.setup_filter_drawer();
        this.setup_enhanced_matching();
    }

    load_saved_filters() {
        frappe.call({
            method: "csf_tz.csftz_hooks.enhanced_bank_reconciliation.get_bank_reconciliation_filters",
            callback: (r) => {
                if (r.message) {
                    this.saved_filters = r.message;
                    this.render_filter_dropdown();
                    
                    // Load default filter if exists
                    const default_filter = this.saved_filters.find(f => f.is_default);
                    if (default_filter) {
                        this.load_filter(default_filter.name);
                    }
                }
            }
        });
    }

    setup_filter_drawer() {
        // Create filter drawer HTML
        const filter_drawer_html = `
            <div class="enhanced-filter-drawer" style="display: none;">
                <div class="filter-drawer-header">
                    <h5>${__('Advanced Filters')}</h5>
                    <button class="btn btn-sm btn-secondary close-drawer">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="filter-drawer-content">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>${__('Description Contains')}</label>
                                <input type="text" class="form-control" id="filter-description" 
                                       placeholder="${__('Enter text or regex pattern')}">
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" id="filter-description-regex">
                                        ${__('Use Regular Expression')}
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>${__('Reference Number')}</label>
                                <input type="text" class="form-control" id="filter-reference" 
                                       placeholder="${__('Reference number')}">
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" id="filter-reference-exact">
                                        ${__('Exact Match')}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>${__('Date Range')}</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <input type="date" class="form-control" id="filter-date-from" 
                                               placeholder="${__('From Date')}">
                                    </div>
                                    <div class="col-md-6">
                                        <input type="date" class="form-control" id="filter-date-to" 
                                               placeholder="${__('To Date')}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>${__('Amount Range')}</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <input type="number" class="form-control" id="filter-amount-from" 
                                               placeholder="${__('Min Amount')}" step="0.01">
                                    </div>
                                    <div class="col-md-6">
                                        <input type="number" class="form-control" id="filter-amount-to" 
                                               placeholder="${__('Max Amount')}" step="0.01">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>${__('Match Status')}</label>
                                <select class="form-control" id="filter-match-status">
                                    <option value="">${__('All')}</option>
                                    <option value="exact">${__('Exact Matches')}</option>
                                    <option value="partial">${__('Partial Matches')}</option>
                                    <option value="suggested">${__('Suggested Matches')}</option>
                                    <option value="unmatched">${__('Unmatched')}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="filter-drawer-actions">
                    <button class="btn btn-primary apply-filters">${__('Apply Filters')}</button>
                    <button class="btn btn-secondary clear-filters">${__('Clear All')}</button>
                    <button class="btn btn-info save-filter">${__('Save Filter')}</button>
                </div>
            </div>
        `;

        // Add filter drawer to page
        $(this.$reconciliation_tool_dt).before(filter_drawer_html);

        // Bind events
        this.bind_filter_events();
    }

    bind_filter_events() {
        const $drawer = $('.enhanced-filter-drawer');

        // Close drawer
        $drawer.find('.close-drawer').on('click', () => {
            $drawer.hide();
        });

        // Apply filters
        $drawer.find('.apply-filters').on('click', () => {
            this.apply_current_filters();
        });

        // Clear filters
        $drawer.find('.clear-filters').on('click', () => {
            this.clear_all_filters();
        });

        // Save filter
        $drawer.find('.save-filter').on('click', () => {
            this.show_save_filter_dialog();
        });

        // Real-time filtering on input change
        $drawer.find('input, select').on('input change', () => {
            clearTimeout(this.filter_timeout);
            this.filter_timeout = setTimeout(() => {
                this.apply_current_filters();
            }, 500);
        });
    }

    setup_enhanced_matching() {
        // Override the original get_linked_payments method
        this.original_get_linked_payments = this.get_linked_payments;
        this.get_linked_payments = this.get_enhanced_linked_payments;
    }

    get_enhanced_linked_payments(bank_transaction_name) {
        return frappe.call({
            method: "csf_tz.csftz_hooks.enhanced_bank_reconciliation.get_enhanced_linked_payments",
            args: {
                bank_transaction_name: bank_transaction_name,
                document_types: this.get_document_types(),
                from_date: this.bank_statement_from_date,
                to_date: this.bank_statement_to_date,
                filter_by_reference_date: this.filter_by_reference_date,
                from_reference_date: this.from_reference_date,
                to_reference_date: this.to_reference_date,
                use_enhanced_matching: true
            },
            callback: (r) => {
                if (r.message) {
                    this.render_enhanced_matches(r.message);
                }
            }
        });
    }

    render_enhanced_matches(data) {
        const { matches, internal_transfer, transaction_info } = data;
        
        // Clear existing content
        this.datatable_container.empty();

        // Add match summary
        this.add_match_summary(matches, internal_transfer);

        // Render matches by category
        this.render_match_category('Exact Matches', matches.exact_matches, 'success');
        this.render_match_category('Partial Matches', matches.partial_matches, 'warning');
        this.render_match_category('Suggested Matches', matches.suggested_matches, 'info');

        // Add internal transfer option if detected
        if (internal_transfer.is_internal_transfer) {
            this.add_internal_transfer_option(internal_transfer, transaction_info);
        }
    }

    add_match_summary(matches, internal_transfer) {
        const total_exact = matches.exact_matches.length;
        const total_partial = matches.partial_matches.length;
        const total_suggested = matches.suggested_matches.length;

        let summary_html = `
            <div class="match-summary alert alert-info">
                <h6>${__('Match Summary')}</h6>
                <div class="row">
                    <div class="col-md-3">
                        <span class="badge badge-success">${total_exact}</span> ${__('Exact Matches')}
                    </div>
                    <div class="col-md-3">
                        <span class="badge badge-warning">${total_partial}</span> ${__('Partial Matches')}
                    </div>
                    <div class="col-md-3">
                        <span class="badge badge-info">${total_suggested}</span> ${__('Suggested Matches')}
                    </div>
        `;

        if (internal_transfer.is_internal_transfer) {
            summary_html += `
                    <div class="col-md-3">
                        <span class="badge badge-primary">
                            <i class="fa fa-exchange"></i>
                        </span> ${__('Internal Transfer Detected')}
                    </div>
            `;
        }

        summary_html += `
                </div>
            </div>
        `;

        $(this.$reconciliation_tool_dt).prepend(summary_html);
    }

    render_match_category(title, matches, badge_class) {
        if (matches.length === 0) return;

        let category_html = `
            <div class="match-category">
                <h6 class="match-category-title">
                    <span class="badge badge-${badge_class}">${matches.length}</span>
                    ${__(title)}
                </h6>
                <div class="match-list">
        `;

        matches.forEach(match => {
            category_html += this.render_match_item(match, badge_class);
        });

        category_html += `
                </div>
            </div>
        `;

        $(this.$reconciliation_tool_dt).append(category_html);
    }

    render_match_item(match, badge_class) {
        const match_details = match.match_details || 'Unknown';
        const match_score = match.match_score || 0;

        return `
            <div class="match-item card mb-2">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="card-title">
                                ${match.doctype} - ${match.name}
                                <span class="badge badge-${badge_class} ml-2">${match_score}%</span>
                            </h6>
                            <p class="card-text">
                                <strong>${__('Amount')}:</strong> ${format_currency(match.paid_amount)} |
                                <strong>${__('Date')}:</strong> ${match.posting_date} |
                                <strong>${__('Reference')}:</strong> ${match.reference_no || 'N/A'}
                            </p>
                            <small class="text-muted">
                                <i class="fa fa-check-circle"></i> ${__('Matched via')}: ${match_details}
                            </small>
                        </div>
                        <div class="col-md-4 text-right">
                            <button class="btn btn-sm btn-primary reconcile-match" 
                                    data-match='${JSON.stringify(match)}'>
                                ${__('Reconcile')}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    add_internal_transfer_option(internal_transfer, transaction_info) {
        const confidence = internal_transfer.confidence || 0;
        const matching_transactions = internal_transfer.matching_transactions || [];

        let transfer_html = `
            <div class="internal-transfer-section alert alert-primary">
                <h6>
                    <i class="fa fa-exchange"></i> ${__('Internal Transfer Detected')}
                    <span class="badge badge-primary ml-2">${confidence}% ${__('Confidence')}</span>
                </h6>
                <p>${__('This transaction appears to be an internal transfer between bank accounts.')}</p>
        `;

        if (matching_transactions.length > 0) {
            transfer_html += `
                <div class="matching-transactions">
                    <h6>${__('Matching Transactions Found')}:</h6>
            `;

            matching_transactions.forEach(match => {
                transfer_html += `
                    <div class="match-transaction">
                        <strong>${match.target_bank_account}</strong> - 
                        ${format_currency(match.deposit || match.withdrawal)} on ${match.date}
                        <button class="btn btn-sm btn-success ml-2 create-internal-transfer" 
                                data-target-account="${match.target_bank_account}"
                                data-transaction='${JSON.stringify(transaction_info)}'>
                            ${__('Create Internal Transfer')}
                        </button>
                    </div>
                `;
            });

            transfer_html += `</div>`;
        }

        transfer_html += `</div>`;

        $(this.$reconciliation_tool_dt).append(transfer_html);

        // Bind internal transfer events
        $('.create-internal-transfer').on('click', (e) => {
            this.create_internal_transfer(e);
        });
    }

    create_internal_transfer(e) {
        const target_account = $(e.target).data('target-account');
        const transaction = $(e.target).data('transaction');

        frappe.call({
            method: "csf_tz.csftz_hooks.enhanced_bank_reconciliation.create_internal_transfer_payment_entry",
            args: {
                bank_transaction_name: transaction.name,
                target_bank_account: target_account,
                reference_number: transaction.reference_number,
                posting_date: transaction.date,
                auto_reconcile: true
            },
            callback: (r) => {
                if (r.message && r.message.status === 'success') {
                    frappe.show_alert({
                        message: __('Internal transfer created and reconciled successfully'),
                        indicator: 'green'
                    });
                    this.refresh_data();
                } else {
                    frappe.show_alert({
                        message: __('Error creating internal transfer'),
                        indicator: 'red'
                    });
                }
            }
        });
    }

    show_filter_drawer() {
        $('.enhanced-filter-drawer').show();
    }

    hide_filter_drawer() {
        $('.enhanced-filter-drawer').hide();
    }

    apply_current_filters() {
        const filters = this.get_current_filter_values();
        this.filters = filters;
        this.refresh_with_filters();
        this.update_filter_badges();
    }

    get_current_filter_values() {
        return {
            description_contains: $('#filter-description').val(),
            description_is_regex: $('#filter-description-regex').is(':checked'),
            reference_number: $('#filter-reference').val(),
            reference_exact_match: $('#filter-reference-exact').is(':checked'),
            date_from: $('#filter-date-from').val(),
            date_to: $('#filter-date-to').val(),
            amount_from: $('#filter-amount-from').val(),
            amount_to: $('#filter-amount-to').val(),
            match_status: $('#filter-match-status').val()
        };
    }

    clear_all_filters() {
        $('#filter-description').val('');
        $('#filter-description-regex').prop('checked', false);
        $('#filter-reference').val('');
        $('#filter-reference-exact').prop('checked', false);
        $('#filter-date-from').val('');
        $('#filter-date-to').val('');
        $('#filter-amount-from').val('');
        $('#filter-amount-to').val('');
        $('#filter-match-status').val('');
        
        this.filters = {};
        this.refresh_with_filters();
        this.update_filter_badges();
    }

    update_filter_badges() {
        // Remove existing badges
        $('.filter-badge').remove();

        // Add badges for active filters
        const active_filters = Object.keys(this.filters).filter(key => 
            this.filters[key] && this.filters[key] !== ''
        );

        if (active_filters.length > 0) {
            let badge_html = '<div class="filter-badges mt-2">';
            active_filters.forEach(filter => {
                const value = this.filters[filter];
                const label = this.get_filter_label(filter);
                badge_html += `
                    <span class="badge badge-info filter-badge mr-1">
                        ${label}: ${value}
                        <i class="fa fa-times ml-1" data-filter="${filter}"></i>
                    </span>
                `;
            });
            badge_html += '</div>';

            $('.enhanced-filter-drawer').after(badge_html);

            // Bind remove filter events
            $('.filter-badge .fa-times').on('click', (e) => {
                const filter_key = $(e.target).data('filter');
                this.remove_filter(filter_key);
            });
        }
    }

    get_filter_label(filter_key) {
        const labels = {
            'description_contains': __('Description'),
            'reference_number': __('Reference'),
            'date_from': __('From Date'),
            'date_to': __('To Date'),
            'amount_from': __('Min Amount'),
            'amount_to': __('Max Amount'),
            'match_status': __('Status')
        };
        return labels[filter_key] || filter_key;
    }

    remove_filter(filter_key) {
        delete this.filters[filter_key];
        $(`#filter-${filter_key.replace('_', '-')}`).val('');
        this.refresh_with_filters();
        this.update_filter_badges();
    }

    show_save_filter_dialog() {
        const filters = this.get_current_filter_values();
        
        if (Object.keys(filters).filter(k => filters[k] && filters[k] !== '').length === 0) {
            frappe.msgprint(__('Please set some filters before saving'));
            return;
        }

        frappe.prompt([
            {
                fieldtype: 'Data',
                fieldname: 'filter_name',
                label: __('Filter Name'),
                reqd: 1
            },
            {
                fieldtype: 'Small Text',
                fieldname: 'description',
                label: __('Description')
            },
            {
                fieldtype: 'Check',
                fieldname: 'is_default',
                label: __('Set as Default')
            }
        ], (values) => {
            this.save_filter(values.filter_name, filters, values.is_default, values.description);
        }, __('Save Filter'));
    }

    save_filter(filter_name, filter_config, is_default = false, description = '') {
        frappe.call({
            method: "csf_tz.csftz_hooks.enhanced_bank_reconciliation.save_bank_reconciliation_filter",
            args: {
                filter_name: filter_name,
                filter_config: filter_config,
                is_default: is_default
            },
            callback: (r) => {
                if (r.message) {
                    frappe.show_alert({
                        message: __('Filter saved successfully'),
                        indicator: 'green'
                    });
                    this.load_saved_filters();
                    this.active_filter_name = filter_name;
                }
            }
        });
    }

    render_filter_dropdown() {
        // Add filter dropdown to toolbar
        if (this.saved_filters.length > 0) {
            let dropdown_html = `
                <div class="saved-filters-dropdown dropdown">
                    <button class="btn btn-secondary dropdown-toggle" type="button" 
                            data-toggle="dropdown">
                        <i class="fa fa-filter"></i> ${__('Saved Filters')}
                    </button>
                    <div class="dropdown-menu">
            `;

            this.saved_filters.forEach(filter => {
                dropdown_html += `
                    <a class="dropdown-item load-filter" href="#" data-filter-name="${filter.name}">
                        ${filter.filter_name}
                        ${filter.is_default ? '<i class="fa fa-star text-warning"></i>' : ''}
                    </a>
                `;
            });

            dropdown_html += `
                    </div>
                </div>
            `;

            // Add to page toolbar
            $('.page-actions').prepend(dropdown_html);

            // Bind events
            $('.load-filter').on('click', (e) => {
                e.preventDefault();
                const filter_name = $(e.target).data('filter-name');
                this.load_filter(filter_name);
            });
        }
    }

    load_filter(filter_name) {
        const filter = this.saved_filters.find(f => f.name === filter_name);
        if (filter) {
            const config = JSON.parse(filter.filter_config);
            this.set_filter_values(config);
            this.apply_current_filters();
            this.active_filter_name = filter.filter_name;
        }
    }

    set_filter_values(config) {
        $('#filter-description').val(config.description_contains || '');
        $('#filter-description-regex').prop('checked', config.description_is_regex || false);
        $('#filter-reference').val(config.reference_number || '');
        $('#filter-reference-exact').prop('checked', config.reference_exact_match || false);
        $('#filter-date-from').val(config.date_from || '');
        $('#filter-date-to').val(config.date_to || '');
        $('#filter-amount-from').val(config.amount_from || '');
        $('#filter-amount-to').val(config.amount_to || '');
        $('#filter-match-status').val(config.match_status || '');
    }

    refresh_with_filters() {
        // This would integrate with the main data refresh logic
        // Implementation depends on how the original data table works
        this.refresh_data();
    }

    refresh_data() {
        // Refresh the main data table
        if (this.make_dt) {
            this.make_dt();
        }
    }
};
