{"actions": [], "allow_rename": 1, "creation": "2025-01-07 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["filter_name", "filter_config", "is_default", "column_break_2", "description", "last_used"], "fields": [{"fieldname": "filter_name", "fieldtype": "Data", "in_list_view": 1, "label": "Filter Name", "reqd": 1, "unique": 1}, {"fieldname": "filter_config", "fieldtype": "Long Text", "label": "Filter Configuration", "reqd": 1}, {"default": "0", "fieldname": "is_default", "fieldtype": "Check", "in_list_view": 1, "label": "<PERSON>"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "description", "fieldtype": "Small Text", "in_list_view": 1, "label": "Description"}, {"fieldname": "last_used", "fieldtype": "Datetime", "label": "Last Used", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-01-07 10:00:00.000000", "modified_by": "Administrator", "module": "CSF TZ", "name": "Bank Reconciliation Filter", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}