# Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
import json
from frappe.model.document import Document


class BankReconciliationFilter(Document):
    def validate(self):
        # Validate filter configuration is valid JSON
        if self.filter_config:
            try:
                json.loads(self.filter_config)
            except json.JSONDecodeError:
                frappe.throw("Filter Configuration must be valid JSON")
        
        # Ensure only one default filter per user
        if self.is_default:
            existing_defaults = frappe.get_all(
                "Bank Reconciliation Filter",
                filters={
                    "owner": self.owner,
                    "is_default": 1,
                    "name": ["!=", self.name]
                }
            )
            
            if existing_defaults:
                frappe.db.set_value(
                    "Bank Reconciliation Filter",
                    existing_defaults[0].name,
                    "is_default",
                    0
                )
    
    def on_update(self):
        # Update last_used timestamp when filter is modified
        if not self.last_used:
            self.db_set("last_used", frappe.utils.now())
