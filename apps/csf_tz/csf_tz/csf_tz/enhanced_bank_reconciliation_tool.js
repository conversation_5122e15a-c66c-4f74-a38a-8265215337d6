/**
 * Enhanced Bank Reconciliation Tool JavaScript Override
 * Extends the original ERPNext Bank Reconciliation Tool with enhanced features
 */

frappe.ui.form.on('Bank Reconciliation Tool', {
    refresh: function(frm) {
        // Add enhanced buttons only after the form is loaded
        setTimeout(function() {
            add_enhanced_buttons(frm);
        }, 1000);
    }
});

function add_enhanced_buttons(frm) {
    if (frm.doc.bank_account) {
        // Add enhanced matching button
        frm.add_custom_button(__("Enhanced Matching"), function() {
            show_enhanced_matching_dialog(frm);
        }, __("Tools"));

        // Add internal transfer button
        frm.add_custom_button(__("Internal Transfers"), function() {
            show_internal_transfer_dialog(frm);
        }, __("Tools"));

        // Add saved filters button
        frm.add_custom_button(__("Saved Filters"), function() {
            show_saved_filters_dialog(frm);
        }, __("Tools"));

        // Add configuration button
        frm.add_custom_button(__("Configuration"), function() {
            show_reconciliation_config_dialog();
        }, __("Tools"));
    }

    // Add help button
    frm.add_custom_button(__("Help"), function() {
        show_enhanced_help_dialog();
    }, __("Tools"));
}

function show_enhanced_matching_dialog(frm) {
    const dialog = new frappe.ui.Dialog({
        title: __('Enhanced Matching'),
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'matching_info',
                options: `
                    <div class="alert alert-info">
                        <h6>Enhanced Matching Features:</h6>
                        <ul>
                            <li><strong>Prioritized Matching:</strong> Reference number + amount gets highest priority</li>
                            <li><strong>Partial Matches:</strong> Shows matches with 60-89% confidence</li>
                            <li><strong>Smart Scoring:</strong> Considers date proximity and description similarity</li>
                            <li><strong>Match Details:</strong> Shows why transactions were matched</li>
                        </ul>
                        <p>The enhanced matching is automatically applied when you use "Get Unreconciled Entries".</p>
                    </div>
                `
            },
            {
                fieldtype: 'Button',
                fieldname: 'test_matching',
                label: __('Test Enhanced Matching'),
                click: function() {
                    test_enhanced_matching(frm);
                }
            }
        ]
    });
    dialog.show();
}

function show_internal_transfer_dialog(frm) {
    const dialog = new frappe.ui.Dialog({
        title: __('Internal Transfer Management'),
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'transfer_info',
                options: `
                    <div class="alert alert-info">
                        <h6>Internal Transfer Features:</h6>
                        <ul>
                            <li><strong>Auto-Detection:</strong> Finds matching transactions in other bank accounts</li>
                            <li><strong>Payment Entry Creation:</strong> Creates internal transfer payment entries</li>
                            <li><strong>Auto-Reconciliation:</strong> Reconciles both sides automatically</li>
                        </ul>
                    </div>
                `
            },
            {
                fieldtype: 'Link',
                fieldname: 'target_bank_account',
                label: __('Target Bank Account'),
                options: 'Bank Account',
                get_query: function() {
                    return {
                        filters: {
                            'company': frm.doc.company,
                            'name': ['!=', frm.doc.bank_account]
                        }
                    };
                }
            },
            {
                fieldtype: 'Button',
                fieldname: 'detect_transfers',
                label: __('Detect Internal Transfers'),
                click: function() {
                    detect_internal_transfers(frm, dialog.get_values());
                }
            }
        ]
    });
    dialog.show();
}

function show_saved_filters_dialog(frm) {
    frappe.call({
        method: 'csf_tz.csftz_hooks.enhanced_bank_reconciliation.get_bank_reconciliation_filters',
        callback: function(r) {
            if (r.message) {
                show_filters_list(r.message, frm);
            }
        }
    });
}

function show_filters_list(filters, frm) {
    let filter_options = '<option value="">Select a saved filter</option>';
    filters.forEach(function(filter) {
        filter_options += `<option value="${filter.name}">${filter.filter_name}${filter.is_default ? ' (Default)' : ''}</option>`;
    });

    const dialog = new frappe.ui.Dialog({
        title: __('Saved Filters'),
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'filters_list',
                options: `
                    <div class="saved-filters-section">
                        <h6>Load Saved Filter:</h6>
                        <select class="form-control" id="saved-filter-select">
                            ${filter_options}
                        </select>
                        <br><br>
                        <button class="btn btn-primary btn-sm" onclick="load_selected_filter()">Load Filter</button>
                        <button class="btn btn-secondary btn-sm" onclick="create_new_filter()">Create New Filter</button>
                    </div>
                `
            }
        ]
    });
    dialog.show();

    // Add global functions for the buttons
    window.load_selected_filter = function() {
        const selected = document.getElementById('saved-filter-select').value;
        if (selected) {
            load_filter(selected, frm);
            dialog.hide();
        }
    };

    window.create_new_filter = function() {
        dialog.hide();
        show_create_filter_dialog(frm);
    };
}

function show_create_filter_dialog(frm) {
    const dialog = new frappe.ui.Dialog({
        title: __('Create New Filter'),
        fields: [
            {
                fieldtype: 'Data',
                fieldname: 'filter_name',
                label: __('Filter Name'),
                reqd: 1
            },
            {
                fieldtype: 'Data',
                fieldname: 'description_contains',
                label: __('Description Contains')
            },
            {
                fieldtype: 'Check',
                fieldname: 'description_is_regex',
                label: __('Use Regular Expression')
            },
            {
                fieldtype: 'Data',
                fieldname: 'reference_number',
                label: __('Reference Number')
            },
            {
                fieldtype: 'Check',
                fieldname: 'reference_exact_match',
                label: __('Exact Reference Match')
            },
            {
                fieldtype: 'Date',
                fieldname: 'date_from',
                label: __('From Date')
            },
            {
                fieldtype: 'Date',
                fieldname: 'date_to',
                label: __('To Date')
            },
            {
                fieldtype: 'Float',
                fieldname: 'amount_from',
                label: __('Minimum Amount')
            },
            {
                fieldtype: 'Float',
                fieldname: 'amount_to',
                label: __('Maximum Amount')
            },
            {
                fieldtype: 'Check',
                fieldname: 'is_default',
                label: __('Set as Default Filter')
            }
        ],
        primary_action: function(values) {
            save_new_filter(values, frm);
            dialog.hide();
        },
        primary_action_label: __('Save Filter')
    });
    dialog.show();
}

function test_enhanced_matching(frm) {
    if (!frm.doc.bank_account) {
        frappe.msgprint(__('Please select a bank account first'));
        return;
    }

    frappe.call({
        method: 'csf_tz.csftz_hooks.enhanced_bank_reconciliation.get_enhanced_linked_payments',
        args: {
            bank_transaction_name: 'test',  // This would be a real transaction name
            use_enhanced_matching: true
        },
        callback: function(r) {
            if (r.message) {
                frappe.msgprint({
                    title: __('Enhanced Matching Test'),
                    message: __('Enhanced matching is working! Check the console for details.'),
                    indicator: 'green'
                });
                console.log('Enhanced matching result:', r.message);
            }
        }
    });
}

function detect_internal_transfers(frm, values) {
    if (!values.target_bank_account) {
        frappe.msgprint(__('Please select a target bank account'));
        return;
    }

    frappe.call({
        method: 'csf_tz.csftz_hooks.enhanced_bank_reconciliation.detect_internal_transfers',
        args: {
            source_bank_account: frm.doc.bank_account,
            target_bank_account: values.target_bank_account,
            from_date: frm.doc.bank_statement_from_date,
            to_date: frm.doc.bank_statement_to_date
        },
        callback: function(r) {
            if (r.message) {
                show_internal_transfer_results(r.message);
            }
        }
    });
}

function show_internal_transfer_results(results) {
    let html = '<div class="internal-transfer-results">';
    if (results.length > 0) {
        html += '<h6>Potential Internal Transfers Found:</h6>';
        results.forEach(function(transfer) {
            html += `
                <div class="transfer-item" style="border: 1px solid #ddd; padding: 10px; margin: 5px 0;">
                    <strong>Amount:</strong> ${transfer.amount}<br>
                    <strong>Date:</strong> ${transfer.date}<br>
                    <strong>Reference:</strong> ${transfer.reference_number || 'N/A'}<br>
                    <strong>Confidence:</strong> ${transfer.confidence}%<br>
                    <button class="btn btn-sm btn-primary" onclick="create_internal_transfer('${transfer.transaction_name}', '${transfer.target_account}')">
                        Create Transfer
                    </button>
                </div>
            `;
        });
    } else {
        html += '<p>No potential internal transfers found.</p>';
    }
    html += '</div>';

    const dialog = new frappe.ui.Dialog({
        title: __('Internal Transfer Results'),
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'results',
                options: html
            }
        ]
    });
    dialog.show();
}

function load_filter(filter_name, frm) {
    frappe.call({
        method: 'frappe.client.get',
        args: {
            doctype: 'Bank Reconciliation Filter',
            name: filter_name
        },
        callback: function(r) {
            if (r.message) {
                const config = JSON.parse(r.message.filter_config);
                apply_filter_config(config, frm);
                frappe.show_alert({
                    message: __('Filter "{0}" loaded successfully', [r.message.filter_name]),
                    indicator: 'green'
                });
            }
        }
    });
}

function save_new_filter(values, frm) {
    const filter_config = {
        description_contains: values.description_contains,
        description_is_regex: values.description_is_regex,
        reference_number: values.reference_number,
        reference_exact_match: values.reference_exact_match,
        date_from: values.date_from,
        date_to: values.date_to,
        amount_from: values.amount_from,
        amount_to: values.amount_to
    };

    frappe.call({
        method: 'csf_tz.csftz_hooks.enhanced_bank_reconciliation.save_bank_reconciliation_filter',
        args: {
            filter_name: values.filter_name,
            filter_config: filter_config,
            is_default: values.is_default
        },
        callback: function(r) {
            if (r.message) {
                frappe.show_alert({
                    message: __('Filter saved successfully'),
                    indicator: 'green'
                });
            }
        }
    });
}

function apply_filter_config(config, frm) {
    // This would apply the filter configuration to the current view
    // For now, just show the configuration
    console.log('Applying filter config:', config);
    frappe.msgprint({
        title: __('Filter Applied'),
        message: __('Filter configuration has been applied. Refresh the reconciliation tool to see results.'),
        indicator: 'blue'
    });
}

function create_internal_transfer(transaction_name, target_account) {
    frappe.call({
        method: 'csf_tz.csftz_hooks.enhanced_bank_reconciliation.create_internal_transfer_payment_entry',
        args: {
            bank_transaction_name: transaction_name,
            target_bank_account: target_account,
            auto_reconcile: true
        },
        callback: function(r) {
            if (r.message && r.message.status === 'success') {
                frappe.show_alert({
                    message: __('Internal transfer created successfully'),
                    indicator: 'green'
                });
            } else {
                frappe.show_alert({
                    message: __('Error creating internal transfer'),
                    indicator: 'red'
                });
            }
        }
    });
}

function show_reconciliation_config_dialog() {
    const dialog = new frappe.ui.Dialog({
        title: __('Bank Reconciliation Configuration'),
        fields: [
            {
                fieldtype: 'Section Break',
                label: __('Matching Settings')
            },
            {
                fieldtype: 'Check',
                fieldname: 'enable_enhanced_matching',
                label: __('Enable Enhanced Matching Logic'),
                default: 1,
                description: __('Use improved matching algorithm with reference number and amount prioritization')
            },
            {
                fieldtype: 'Check',
                fieldname: 'enable_partial_matching',
                label: __('Enable Partial Matching'),
                default: 1,
                description: __('Show partial matches for manual verification')
            },
            {
                fieldtype: 'Float',
                fieldname: 'amount_tolerance_percentage',
                label: __('Amount Tolerance (%)'),
                default: 5.0,
                description: __('Percentage tolerance for amount matching')
            },
            {
                fieldtype: 'Int',
                fieldname: 'date_tolerance_days',
                label: __('Date Tolerance (Days)'),
                default: 7,
                description: __('Number of days tolerance for date matching')
            },
            {
                fieldtype: 'Section Break',
                label: __('Internal Transfer Settings')
            },
            {
                fieldtype: 'Check',
                fieldname: 'enable_internal_transfers',
                label: __('Enable Internal Transfer Detection'),
                default: 1,
                description: __('Automatically detect and suggest internal transfers')
            },
            {
                fieldtype: 'Check',
                fieldname: 'auto_reconcile_internal_transfers',
                label: __('Auto-reconcile Internal Transfers'),
                default: 0,
                description: __('Automatically reconcile both sides of internal transfers when found')
            },
            {
                fieldtype: 'Int',
                fieldname: 'internal_transfer_confidence_threshold',
                label: __('Internal Transfer Confidence Threshold (%)'),
                default: 70,
                description: __('Minimum confidence level to suggest internal transfers')
            },
            {
                fieldtype: 'Section Break',
                label: __('UI Settings')
            },
            {
                fieldtype: 'Check',
                fieldname: 'show_match_scores',
                label: __('Show Match Scores'),
                default: 1,
                description: __('Display match confidence scores in the UI')
            },
            {
                fieldtype: 'Check',
                fieldname: 'group_by_match_type',
                label: __('Group by Match Type'),
                default: 1,
                description: __('Group matches by type (Exact, Partial, Suggested)')
            },
            {
                fieldtype: 'Check',
                fieldname: 'persistent_filters',
                label: __('Persistent Filters'),
                default: 1,
                description: __('Remember filter settings across sessions')
            }
        ],
        primary_action: function(values) {
            // Save configuration
            frappe.call({
                method: 'frappe.client.set_value',
                args: {
                    doctype: 'Singles',
                    name: 'Bank Reconciliation Settings',
                    fieldname: values
                },
                callback: function(r) {
                    if (!r.exc) {
                        frappe.show_alert({
                            message: __('Configuration saved successfully'),
                            indicator: 'green'
                        });
                        dialog.hide();
                    }
                }
            });
        },
        primary_action_label: __('Save Configuration')
    });

    // Load current configuration
    frappe.call({
        method: 'frappe.client.get_value',
        args: {
            doctype: 'Singles',
            name: 'Bank Reconciliation Settings',
            fieldname: [
                'enable_enhanced_matching',
                'enable_partial_matching',
                'amount_tolerance_percentage',
                'date_tolerance_days',
                'enable_internal_transfers',
                'auto_reconcile_internal_transfers',
                'internal_transfer_confidence_threshold',
                'show_match_scores',
                'group_by_match_type',
                'persistent_filters'
            ]
        },
        callback: function(r) {
            if (r.message) {
                dialog.set_values(r.message);
            }
            dialog.show();
        }
    });
}

function show_enhanced_help_dialog() {
    const help_content = `
        <div class="enhanced-help-content">
            <h4>${__('Enhanced Bank Reconciliation Tool')}</h4>
            
            <h5>${__('Enhanced Matching Logic')}</h5>
            <p>${__('The enhanced matching system prioritizes matches based on:')}</p>
            <ul>
                <li><strong>${__('Reference Number + Amount')}:</strong> ${__('Highest priority for exact matches')}</li>
                <li><strong>${__('Partial Matches')}:</strong> ${__('Reference OR Amount + Party matches')}</li>
                <li><strong>${__('Suggested Matches')}:</strong> ${__('Description similarity + Amount range')}</li>
            </ul>

            <h5>${__('Internal Transfer Support')}</h5>
            <p>${__('The system can automatically detect internal transfers between company bank accounts and:')}</p>
            <ul>
                <li>${__('Create Payment Entry (Internal Transfer) documents')}</li>
                <li>${__('Auto-reconcile both sides when matching transactions are found')}</li>
                <li>${__('Handle partial reconciliation when only one side is available')}</li>
            </ul>

            <h5>${__('Advanced Filters')}</h5>
            <p>${__('Use the Advanced Filters panel to:')}</p>
            <ul>
                <li><strong>${__('Description Contains')}:</strong> ${__('Filter by text or regex patterns')}</li>
                <li><strong>${__('Reference Number')}:</strong> ${__('Exact or partial reference matching')}</li>
                <li><strong>${__('Date Range')}:</strong> ${__('Filter transactions by date range')}</li>
                <li><strong>${__('Amount Range')}:</strong> ${__('Filter by minimum and maximum amounts')}</li>
                <li><strong>${__('Match Status')}:</strong> ${__('Filter by match confidence level')}</li>
            </ul>

            <h5>${__('Saved Filters')}</h5>
            <p>${__('You can save frequently used filter combinations:')}</p>
            <ul>
                <li>${__('Save filter sets with custom names')}</li>
                <li>${__('Set default filters that load automatically')}</li>
                <li>${__('Share filters with other users (if permissions allow)')}</li>
                <li>${__('Filters persist across browser sessions')}</li>
            </ul>

            <h5>${__('Match Indicators')}</h5>
            <p>${__('The system displays match confidence and details:')}</p>
            <ul>
                <li><span class="badge badge-success">${__('Green')}</span>: ${__('Exact matches (90%+ confidence)')}</li>
                <li><span class="badge badge-warning">${__('Yellow')}</span>: ${__('Partial matches (60-89% confidence)')}</li>
                <li><span class="badge badge-info">${__('Blue')}</span>: ${__('Suggested matches (<60% confidence)')}</li>
                <li><span class="badge badge-primary"><i class="fa fa-exchange"></i></span>: ${__('Internal transfer detected')}</li>
            </ul>

            <h5>${__('Best Practices')}</h5>
            <ul>
                <li>${__('Review partial matches manually before reconciling')}</li>
                <li>${__('Use saved filters for common reconciliation scenarios')}</li>
                <li>${__('Verify internal transfers before auto-reconciliation')}</li>
                <li>${__('Check match details to understand why transactions were matched')}</li>
            </ul>
        </div>
    `;

    const help_dialog = new frappe.ui.Dialog({
        title: __('Enhanced Bank Reconciliation Help'),
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'help_content',
                options: help_content
            }
        ],
        size: 'large'
    });

    help_dialog.show();
}

// Add CSS for enhanced features
frappe.ready(function() {
    const enhanced_css = `
        <style>
        .enhanced-filter-drawer {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .filter-drawer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        .filter-drawer-actions {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #dee2e6;
        }

        .match-summary {
            margin-bottom: 1rem;
        }

        .match-category {
            margin-bottom: 1.5rem;
        }

        .match-category-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        .match-item {
            transition: all 0.2s ease;
        }

        .match-item:hover {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .internal-transfer-section {
            border-left: 4px solid #007bff;
        }

        .filter-badges {
            margin-bottom: 1rem;
        }

        .filter-badge {
            cursor: pointer;
        }

        .filter-badge:hover {
            background-color: #6c757d !important;
        }

        .saved-filters-dropdown {
            margin-right: 0.5rem;
        }

        .enhanced-help-content h5 {
            color: #495057;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }

        .enhanced-help-content ul {
            margin-bottom: 1rem;
        }

        .enhanced-help-content .badge {
            margin-right: 0.25rem;
        }
        </style>
    `;

    $('head').append(enhanced_css);
});
