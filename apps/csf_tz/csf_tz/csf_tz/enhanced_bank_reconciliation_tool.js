/**
 * Enhanced Bank Reconciliation Tool JavaScript Override
 * Extends the original ERPNext Bank Reconciliation Tool with enhanced features
 */

frappe.ui.form.on('Bank Reconciliation Tool', {
    onload: function(frm) {
        // Load enhanced bank reconciliation scripts
        frappe.require([
            '/assets/csf_tz/js/enhanced_bank_reconciliation.js'
        ], function() {
            // Enhanced features loaded
            console.log('Enhanced Bank Reconciliation features loaded');
        });
    },

    refresh: function(frm) {
        // Add enhanced buttons
        if (frm.doc.bank_account) {
            // Add filter drawer toggle button
            frm.add_custom_button(__("Advanced Filters"), function() {
                if (frm.enhanced_data_table_manager) {
                    frm.enhanced_data_table_manager.show_filter_drawer();
                }
            }, __("Tools"));

            // Add configuration button
            frm.add_custom_button(__("Configuration"), function() {
                show_reconciliation_config_dialog();
            }, __("Tools"));

            // Add export filtered results button
            frm.add_custom_button(__("Export Filtered Results"), function() {
                if (frm.enhanced_data_table_manager) {
                    frm.enhanced_data_table_manager.export_filtered_results();
                }
            }, __("Tools"));
        }

        // Add help button
        frm.add_custom_button(__("Help"), function() {
            show_enhanced_help_dialog();
        }, __("Tools"));
    },

    make_reconciliation_tool: function(frm) {
        // Override the original render method to use enhanced data table manager
        if (frm.doc.bank_account) {
            // Use enhanced data table manager instead of original
            frm.enhanced_data_table_manager = new csf_tz.bank_reconciliation.EnhancedDataTableManager({
                company: frm.doc.company,
                bank_account: frm.doc.bank_account,
                $reconciliation_tool_dt: frm.get_field("reconciliation_tool_dt").$wrapper,
                $no_bank_transactions: frm.get_field("no_bank_transactions").$wrapper,
                bank_statement_from_date: frm.doc.bank_statement_from_date,
                bank_statement_to_date: frm.doc.bank_statement_to_date,
                filter_by_reference_date: frm.doc.filter_by_reference_date,
                from_reference_date: frm.doc.from_reference_date,
                to_reference_date: frm.doc.to_reference_date,
                bank_statement_closing_balance: frm.doc.bank_statement_closing_balance,
                cards_manager: frm.cards_manager,
            });
        }
    }
});

function show_reconciliation_config_dialog() {
    const dialog = new frappe.ui.Dialog({
        title: __('Bank Reconciliation Configuration'),
        fields: [
            {
                fieldtype: 'Section Break',
                label: __('Matching Settings')
            },
            {
                fieldtype: 'Check',
                fieldname: 'enable_enhanced_matching',
                label: __('Enable Enhanced Matching Logic'),
                default: 1,
                description: __('Use improved matching algorithm with reference number and amount prioritization')
            },
            {
                fieldtype: 'Check',
                fieldname: 'enable_partial_matching',
                label: __('Enable Partial Matching'),
                default: 1,
                description: __('Show partial matches for manual verification')
            },
            {
                fieldtype: 'Float',
                fieldname: 'amount_tolerance_percentage',
                label: __('Amount Tolerance (%)'),
                default: 5.0,
                description: __('Percentage tolerance for amount matching')
            },
            {
                fieldtype: 'Int',
                fieldname: 'date_tolerance_days',
                label: __('Date Tolerance (Days)'),
                default: 7,
                description: __('Number of days tolerance for date matching')
            },
            {
                fieldtype: 'Section Break',
                label: __('Internal Transfer Settings')
            },
            {
                fieldtype: 'Check',
                fieldname: 'enable_internal_transfers',
                label: __('Enable Internal Transfer Detection'),
                default: 1,
                description: __('Automatically detect and suggest internal transfers')
            },
            {
                fieldtype: 'Check',
                fieldname: 'auto_reconcile_internal_transfers',
                label: __('Auto-reconcile Internal Transfers'),
                default: 0,
                description: __('Automatically reconcile both sides of internal transfers when found')
            },
            {
                fieldtype: 'Int',
                fieldname: 'internal_transfer_confidence_threshold',
                label: __('Internal Transfer Confidence Threshold (%)'),
                default: 70,
                description: __('Minimum confidence level to suggest internal transfers')
            },
            {
                fieldtype: 'Section Break',
                label: __('UI Settings')
            },
            {
                fieldtype: 'Check',
                fieldname: 'show_match_scores',
                label: __('Show Match Scores'),
                default: 1,
                description: __('Display match confidence scores in the UI')
            },
            {
                fieldtype: 'Check',
                fieldname: 'group_by_match_type',
                label: __('Group by Match Type'),
                default: 1,
                description: __('Group matches by type (Exact, Partial, Suggested)')
            },
            {
                fieldtype: 'Check',
                fieldname: 'persistent_filters',
                label: __('Persistent Filters'),
                default: 1,
                description: __('Remember filter settings across sessions')
            }
        ],
        primary_action: function(values) {
            // Save configuration
            frappe.call({
                method: 'frappe.client.set_value',
                args: {
                    doctype: 'Singles',
                    name: 'Bank Reconciliation Settings',
                    fieldname: values
                },
                callback: function(r) {
                    if (!r.exc) {
                        frappe.show_alert({
                            message: __('Configuration saved successfully'),
                            indicator: 'green'
                        });
                        dialog.hide();
                    }
                }
            });
        },
        primary_action_label: __('Save Configuration')
    });

    // Load current configuration
    frappe.call({
        method: 'frappe.client.get_value',
        args: {
            doctype: 'Singles',
            name: 'Bank Reconciliation Settings',
            fieldname: [
                'enable_enhanced_matching',
                'enable_partial_matching',
                'amount_tolerance_percentage',
                'date_tolerance_days',
                'enable_internal_transfers',
                'auto_reconcile_internal_transfers',
                'internal_transfer_confidence_threshold',
                'show_match_scores',
                'group_by_match_type',
                'persistent_filters'
            ]
        },
        callback: function(r) {
            if (r.message) {
                dialog.set_values(r.message);
            }
            dialog.show();
        }
    });
}

function show_enhanced_help_dialog() {
    const help_content = `
        <div class="enhanced-help-content">
            <h4>${__('Enhanced Bank Reconciliation Tool')}</h4>
            
            <h5>${__('Enhanced Matching Logic')}</h5>
            <p>${__('The enhanced matching system prioritizes matches based on:')}</p>
            <ul>
                <li><strong>${__('Reference Number + Amount')}:</strong> ${__('Highest priority for exact matches')}</li>
                <li><strong>${__('Partial Matches')}:</strong> ${__('Reference OR Amount + Party matches')}</li>
                <li><strong>${__('Suggested Matches')}:</strong> ${__('Description similarity + Amount range')}</li>
            </ul>

            <h5>${__('Internal Transfer Support')}</h5>
            <p>${__('The system can automatically detect internal transfers between company bank accounts and:')}</p>
            <ul>
                <li>${__('Create Payment Entry (Internal Transfer) documents')}</li>
                <li>${__('Auto-reconcile both sides when matching transactions are found')}</li>
                <li>${__('Handle partial reconciliation when only one side is available')}</li>
            </ul>

            <h5>${__('Advanced Filters')}</h5>
            <p>${__('Use the Advanced Filters panel to:')}</p>
            <ul>
                <li><strong>${__('Description Contains')}:</strong> ${__('Filter by text or regex patterns')}</li>
                <li><strong>${__('Reference Number')}:</strong> ${__('Exact or partial reference matching')}</li>
                <li><strong>${__('Date Range')}:</strong> ${__('Filter transactions by date range')}</li>
                <li><strong>${__('Amount Range')}:</strong> ${__('Filter by minimum and maximum amounts')}</li>
                <li><strong>${__('Match Status')}:</strong> ${__('Filter by match confidence level')}</li>
            </ul>

            <h5>${__('Saved Filters')}</h5>
            <p>${__('You can save frequently used filter combinations:')}</p>
            <ul>
                <li>${__('Save filter sets with custom names')}</li>
                <li>${__('Set default filters that load automatically')}</li>
                <li>${__('Share filters with other users (if permissions allow)')}</li>
                <li>${__('Filters persist across browser sessions')}</li>
            </ul>

            <h5>${__('Match Indicators')}</h5>
            <p>${__('The system displays match confidence and details:')}</p>
            <ul>
                <li><span class="badge badge-success">${__('Green')}</span>: ${__('Exact matches (90%+ confidence)')}</li>
                <li><span class="badge badge-warning">${__('Yellow')}</span>: ${__('Partial matches (60-89% confidence)')}</li>
                <li><span class="badge badge-info">${__('Blue')}</span>: ${__('Suggested matches (<60% confidence)')}</li>
                <li><span class="badge badge-primary"><i class="fa fa-exchange"></i></span>: ${__('Internal transfer detected')}</li>
            </ul>

            <h5>${__('Best Practices')}</h5>
            <ul>
                <li>${__('Review partial matches manually before reconciling')}</li>
                <li>${__('Use saved filters for common reconciliation scenarios')}</li>
                <li>${__('Verify internal transfers before auto-reconciliation')}</li>
                <li>${__('Check match details to understand why transactions were matched')}</li>
            </ul>
        </div>
    `;

    const help_dialog = new frappe.ui.Dialog({
        title: __('Enhanced Bank Reconciliation Help'),
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'help_content',
                options: help_content
            }
        ],
        size: 'large'
    });

    help_dialog.show();
}

// Add CSS for enhanced features
frappe.ready(function() {
    const enhanced_css = `
        <style>
        .enhanced-filter-drawer {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .filter-drawer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        .filter-drawer-actions {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #dee2e6;
        }

        .match-summary {
            margin-bottom: 1rem;
        }

        .match-category {
            margin-bottom: 1.5rem;
        }

        .match-category-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        .match-item {
            transition: all 0.2s ease;
        }

        .match-item:hover {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .internal-transfer-section {
            border-left: 4px solid #007bff;
        }

        .filter-badges {
            margin-bottom: 1rem;
        }

        .filter-badge {
            cursor: pointer;
        }

        .filter-badge:hover {
            background-color: #6c757d !important;
        }

        .saved-filters-dropdown {
            margin-right: 0.5rem;
        }

        .enhanced-help-content h5 {
            color: #495057;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }

        .enhanced-help-content ul {
            margin-bottom: 1rem;
        }

        .enhanced-help-content .badge {
            margin-right: 0.25rem;
        }
        </style>
    `;

    $('head').append(enhanced_css);
});
