# Enhanced Bank Reconciliation Tool for ERPNext

## Overview

The Enhanced Bank Reconciliation Tool extends ERPNext's standard bank reconciliation functionality with advanced matching logic, internal transfer support, comprehensive filtering, and improved user interface. This implementation addresses the limitations of the standard tool and provides a more robust solution for bank reconciliation workflows.

## Features

### 1. Enhanced Matching Logic

#### Prioritized Matching Algorithm
The enhanced system uses a sophisticated scoring algorithm that prioritizes matches based on:

1. **Exact Matches (90-100% confidence)**
   - Reference number + exact amount match
   - Reference number + amount + party match
   - Reference number + amount + date proximity

2. **Partial Matches (60-89% confidence)**
   - Reference number match with different amount (within tolerance)
   - Exact amount + party match without reference
   - Amount + date proximity + description similarity

3. **Suggested Matches (<60% confidence)**
   - Description keyword similarity + amount range
   - Party match + date proximity
   - Amount within tolerance range

#### Match Scoring Criteria
- **Reference Number Match**: 40 points
- **Exact Amount Match**: 30 points
- **Amount within 5% tolerance**: 15 points
- **Party Match**: 15 points
- **Date Proximity**: 10 points (decreasing with distance)
- **Description Similarity**: 5 points

### 2. Internal Transfer Support

#### Automatic Detection
The system automatically detects potential internal transfers by:
- Finding opposite transactions (deposit vs withdrawal) in other company bank accounts
- Matching by reference number, amount, and date proximity
- Analyzing description patterns for transfer keywords

#### Internal Transfer Creation
When an internal transfer is detected, the system can:
- Create Payment Entry (Internal Transfer) documents
- Pre-fill all relevant fields from bank transaction data
- Validate that source and target accounts are different
- Ensure both accounts belong to the same company

#### Auto-Reconciliation
The system supports automatic reconciliation of both sides:
- Reconciles the source transaction immediately
- Searches for matching transaction in target account
- Auto-reconciles the target transaction if found
- Marks partial reconciliation if only one side is available

### 3. Advanced Filtering System

#### Filter Types
- **Description Contains**: Text or regex pattern matching
- **Reference Number**: Exact or partial matching
- **Date Range**: Filter by transaction date range
- **Amount Range**: Filter by minimum and maximum amounts
- **Match Status**: Filter by confidence level (Exact/Partial/Suggested/Unmatched)

#### Saved Filters
- Save frequently used filter combinations with custom names
- Set default filters that load automatically
- Rename and delete saved filters
- Filters persist across browser sessions using local storage

#### Filter Management
- Real-time filtering with 500ms debounce
- Visual filter badges showing active filters
- One-click filter removal
- Clear all filters functionality

### 4. UI Enhancements

#### Enhanced Data Display
- Match summary cards showing counts by category
- Color-coded match indicators (Green/Yellow/Blue/Red)
- Match confidence scores and details
- Expandable match categories

#### Filter Drawer
- Collapsible side panel for advanced filters
- Intuitive form controls for all filter types
- Save/load filter functionality
- Real-time filter application

#### Match Labels
- Clear indication of why transactions were matched
- "Matched via: Reference + Amount" style labels
- Confidence percentage display
- Internal transfer detection badges

## Implementation Details

### File Structure
```
apps/csf_tz/csf_tz/csftz_hooks/
├── enhanced_bank_reconciliation.py          # Core Python logic
├── test_enhanced_bank_reconciliation.py     # Test suite
└── ENHANCED_BANK_RECONCILIATION_README.md   # This documentation

apps/csf_tz/csf_tz/csf_tz/doctype/
└── bank_reconciliation_filter/              # Filter storage DocType
    ├── bank_reconciliation_filter.json
    ├── bank_reconciliation_filter.py
    └── __init__.py

apps/csf_tz/csf_tz/public/js/
└── enhanced_bank_reconciliation.js          # Enhanced UI components

apps/csf_tz/csf_tz/csf_tz/
└── enhanced_bank_reconciliation_tool.js     # Form override
```

### Core Classes

#### EnhancedMatchingEngine
Handles the improved matching logic with scoring algorithm.

**Key Methods:**
- `get_enhanced_matches()`: Main matching function
- `_calculate_match_score()`: Scoring algorithm
- `_determine_match_type()`: Categorizes matches
- `_get_match_details()`: Provides match explanations

#### InternalTransferManager
Manages internal transfer detection and creation.

**Key Methods:**
- `detect_internal_transfer()`: Finds potential transfers
- `_find_matching_internal_transactions()`: Searches other accounts
- `_calculate_transfer_confidence()`: Confidence scoring

#### BankReconciliationFilters
Handles filter save/load and application.

**Key Methods:**
- `save_filter()`: Persists filter configurations
- `get_saved_filters()`: Retrieves user's saved filters
- `apply_filters()`: Applies filters to transaction lists

### API Endpoints

#### Enhanced Matching
```python
@frappe.whitelist()
def get_enhanced_linked_payments(
    bank_transaction_name,
    document_types=None,
    from_date=None,
    to_date=None,
    filter_by_reference_date=None,
    from_reference_date=None,
    to_reference_date=None,
    use_enhanced_matching=True
)
```

#### Internal Transfer Creation
```python
@frappe.whitelist()
def create_internal_transfer_payment_entry(
    bank_transaction_name,
    target_bank_account,
    reference_number=None,
    reference_date=None,
    posting_date=None,
    remarks=None,
    auto_reconcile=True
)
```

#### Filter Management
```python
@frappe.whitelist()
def get_bank_reconciliation_filters(user=None)

@frappe.whitelist()
def save_bank_reconciliation_filter(filter_name, filter_config, is_default=False, user=None)

@frappe.whitelist()
def delete_bank_reconciliation_filter(filter_name, user=None)
```

## Installation and Setup

### 1. Install the Enhanced Features

The enhanced bank reconciliation tool is part of the CSF TZ app. Ensure the app is installed and updated:

```bash
bench get-app csf_tz
bench install-app csf_tz
bench migrate
```

### 2. Enable Enhanced Features

Navigate to Bank Reconciliation Tool and click "Configuration" to enable:
- Enhanced Matching Logic
- Internal Transfer Detection
- Advanced Filtering
- UI Enhancements

### 3. Configure Settings

Adjust the following settings based on your requirements:
- Amount tolerance percentage (default: 5%)
- Date tolerance in days (default: 7 days)
- Internal transfer confidence threshold (default: 70%)
- Auto-reconciliation preferences

## Usage Guide

### Basic Workflow

1. **Open Bank Reconciliation Tool**
   - Navigate to Accounts > Bank Reconciliation Tool
   - Select bank account and date range
   - Click "Get Unreconciled Entries"

2. **Review Enhanced Matches**
   - Exact matches are shown in green with high confidence
   - Partial matches in yellow require manual verification
   - Suggested matches in blue are low confidence
   - Internal transfers are highlighted with transfer icon

3. **Use Advanced Filters**
   - Click "Advanced Filters" to open filter drawer
   - Set criteria like description patterns, amount ranges
   - Save frequently used filters for future use
   - Apply filters in real-time

4. **Handle Internal Transfers**
   - Review detected internal transfers
   - Click "Create Internal Transfer" for confirmed transfers
   - System will auto-reconcile both sides if possible

### Best Practices

#### Matching Strategy
1. **Start with Exact Matches**: Process high-confidence matches first
2. **Review Partial Matches**: Manually verify before reconciling
3. **Use Filters**: Apply saved filters for common scenarios
4. **Check Internal Transfers**: Verify transfer details before creation

#### Filter Usage
1. **Create Scenario-Based Filters**: Save filters for different reconciliation types
2. **Use Regex for Complex Patterns**: Leverage regex for advanced description matching
3. **Combine Multiple Criteria**: Use amount + description + date for precision
4. **Set Default Filters**: Configure commonly used filters as defaults

#### Internal Transfer Handling
1. **Verify Account Mapping**: Ensure correct source and target accounts
2. **Check Reference Numbers**: Confirm reference numbers match between sides
3. **Review Amounts**: Verify amounts are identical (accounting for currency)
4. **Monitor Auto-Reconciliation**: Check that both sides are properly reconciled

## Testing

### Test Suite
The implementation includes comprehensive tests covering:

#### Unit Tests
- Enhanced matching algorithm accuracy
- Internal transfer detection logic
- Filter save/load functionality
- Edge case handling

#### Integration Tests
- End-to-end reconciliation workflows
- Internal transfer creation and reconciliation
- Filter application and persistence
- UI component interactions

#### Edge Case Tests
- Identical references with different amounts
- Empty references with description matching
- Internal transfers with only one leg
- Multiple matches with same confidence

### Running Tests
```python
# Run all enhanced bank reconciliation tests
from csf_tz.csftz_hooks.test_enhanced_bank_reconciliation import run_enhanced_bank_reconciliation_tests
run_enhanced_bank_reconciliation_tests()

# Run specific test class
import unittest
from csf_tz.csftz_hooks.test_enhanced_bank_reconciliation import TestEnhancedBankReconciliation
suite = unittest.TestLoader().loadTestsFromTestCase(TestEnhancedBankReconciliation)
unittest.TextTestRunner(verbosity=2).run(suite)
```

## Configuration Options

### System Settings
Configure the enhanced features through the Bank Reconciliation Configuration dialog:

#### Matching Settings
- **Enable Enhanced Matching Logic**: Use improved algorithm
- **Enable Partial Matching**: Show partial matches for review
- **Amount Tolerance (%)**: Percentage tolerance for amount matching
- **Date Tolerance (Days)**: Days tolerance for date matching

#### Internal Transfer Settings
- **Enable Internal Transfer Detection**: Auto-detect transfers
- **Auto-reconcile Internal Transfers**: Automatically reconcile both sides
- **Confidence Threshold (%)**: Minimum confidence for suggestions

#### UI Settings
- **Show Match Scores**: Display confidence percentages
- **Group by Match Type**: Organize matches by confidence level
- **Persistent Filters**: Remember filter settings across sessions

### User Preferences
Individual users can customize:
- Default filter sets
- UI layout preferences
- Notification settings
- Auto-reconciliation preferences

## Troubleshooting

### Common Issues

#### Matches Not Appearing
1. Check if enhanced matching is enabled
2. Verify date ranges include transaction dates
3. Ensure bank account is correctly selected
4. Check if filters are too restrictive

#### Internal Transfers Not Detected
1. Verify both bank accounts belong to same company
2. Check reference numbers match between transactions
3. Ensure amounts are identical
4. Confirm dates are within tolerance range

#### Filters Not Saving
1. Check user permissions for Bank Reconciliation Filter DocType
2. Verify filter configuration is valid JSON
3. Ensure filter name is unique for user
4. Check for browser storage limitations

#### Performance Issues
1. Reduce date range for large datasets
2. Use more specific filters to limit results
3. Consider indexing on frequently filtered fields
4. Monitor server resources during reconciliation

### Error Messages

#### "Internal transfers can only be made between accounts of the same company"
- Ensure source and target bank accounts belong to the same company
- Check bank account company assignment

#### "Source and target bank accounts must be different"
- Select different bank accounts for internal transfer
- Verify account selection is correct

#### "Filter Configuration must be valid JSON"
- Check filter configuration syntax
- Ensure all quotes and brackets are properly closed
- Validate JSON format before saving

## Support and Maintenance

### Logging
The system logs important events:
- Match scoring details
- Internal transfer creation
- Filter application results
- Error conditions

### Monitoring
Monitor the following metrics:
- Match accuracy rates
- Internal transfer detection success
- Filter usage patterns
- Performance metrics

### Updates
Keep the enhanced features updated:
- Regular app updates through bench
- Monitor for new ERPNext versions
- Test enhanced features after updates
- Review and update configurations

For technical support, contact the CSF TZ development team or refer to the ERPNext community forums.
