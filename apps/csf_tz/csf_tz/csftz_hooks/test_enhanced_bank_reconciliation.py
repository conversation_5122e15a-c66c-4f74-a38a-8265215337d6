"""
Test cases for Enhanced Bank Reconciliation Tool
"""

import frappe
import unittest
import json
from frappe.utils import nowdate, add_days, flt
from erpnext.accounts.doctype.bank_transaction.test_bank_transaction import (
    create_bank_account, create_gl_account
)
from csf_tz.csftz_hooks.enhanced_bank_reconciliation import (
    EnhancedMatchingEngine, InternalTransferManager, BankReconciliationFilters
)


class TestEnhancedBankReconciliation(unittest.TestCase):
    
    def setUp(self):
        """Set up test data"""
        self.company = "_Test Company"
        
        # Create test bank accounts
        self.bank_account_1 = self._create_test_bank_account("Test Bank 1")
        self.bank_account_2 = self._create_test_bank_account("Test Bank 2")
        
        # Create test transactions
        self.test_transactions = self._create_test_transactions()
        
        # Create test payment entries
        self.test_payment_entries = self._create_test_payment_entries()
    
    def _create_test_bank_account(self, account_name):
        """Create a test bank account"""
        gl_account = create_gl_account(f"_Test {account_name}")
        bank_account = create_bank_account(
            gl_account=gl_account,
            bank_account_name=account_name
        )
        return bank_account
    
    def _create_test_transactions(self):
        """Create test bank transactions"""
        transactions = []
        
        # Transaction 1: Exact match scenario
        trans1 = frappe.get_doc({
            "doctype": "Bank Transaction",
            "bank_account": self.bank_account_1,
            "date": nowdate(),
            "deposit": 1000.00,
            "reference_number": "REF001",
            "description": "Payment from Customer A",
            "party_type": "Customer",
            "party": "_Test Customer"
        })
        trans1.insert()
        trans1.submit()
        transactions.append(trans1)
        
        # Transaction 2: Partial match scenario (amount only)
        trans2 = frappe.get_doc({
            "doctype": "Bank Transaction",
            "bank_account": self.bank_account_1,
            "date": nowdate(),
            "deposit": 2000.00,
            "reference_number": "REF002",
            "description": "Payment from Customer B",
            "party_type": "Customer",
            "party": "_Test Customer 2"
        })
        trans2.insert()
        trans2.submit()
        transactions.append(trans2)
        
        # Transaction 3: Internal transfer scenario
        trans3 = frappe.get_doc({
            "doctype": "Bank Transaction",
            "bank_account": self.bank_account_1,
            "date": nowdate(),
            "withdrawal": 5000.00,
            "reference_number": "INT001",
            "description": "Internal transfer to Account 2"
        })
        trans3.insert()
        trans3.submit()
        transactions.append(trans3)
        
        # Transaction 4: Matching internal transfer
        trans4 = frappe.get_doc({
            "doctype": "Bank Transaction",
            "bank_account": self.bank_account_2,
            "date": nowdate(),
            "deposit": 5000.00,
            "reference_number": "INT001",
            "description": "Internal transfer from Account 1"
        })
        trans4.insert()
        trans4.submit()
        transactions.append(trans4)
        
        return transactions
    
    def _create_test_payment_entries(self):
        """Create test payment entries for matching"""
        payment_entries = []
        
        # Payment Entry 1: Exact match with Transaction 1
        pe1 = frappe.get_doc({
            "doctype": "Payment Entry",
            "payment_type": "Receive",
            "company": self.company,
            "posting_date": nowdate(),
            "paid_to": self.bank_account_1.account,
            "paid_amount": 1000.00,
            "received_amount": 1000.00,
            "reference_no": "REF001",
            "party_type": "Customer",
            "party": "_Test Customer"
        })
        pe1.insert()
        pe1.submit()
        payment_entries.append(pe1)
        
        # Payment Entry 2: Amount match only with Transaction 2
        pe2 = frappe.get_doc({
            "doctype": "Payment Entry",
            "payment_type": "Receive",
            "company": self.company,
            "posting_date": nowdate(),
            "paid_to": self.bank_account_1.account,
            "paid_amount": 2000.00,
            "received_amount": 2000.00,
            "reference_no": "DIFFERENT_REF",
            "party_type": "Customer",
            "party": "_Test Customer 2"
        })
        pe2.insert()
        pe2.submit()
        payment_entries.append(pe2)
        
        return payment_entries
    
    def test_enhanced_matching_engine_exact_match(self):
        """Test exact match detection"""
        engine = EnhancedMatchingEngine(self.bank_account_1.account, self.company)
        
        transaction = self.test_transactions[0]  # Transaction with REF001
        matches = engine.get_enhanced_matches(
            transaction,
            document_types=["payment_entry"]
        )
        
        # Should find exact match
        self.assertTrue(len(matches['exact_matches']) > 0)
        exact_match = matches['exact_matches'][0]
        self.assertEqual(exact_match['match_score'], 100)  # Perfect score
        self.assertIn("Reference Number", exact_match['match_details'])
        self.assertIn("Exact Amount", exact_match['match_details'])
    
    def test_enhanced_matching_engine_partial_match(self):
        """Test partial match detection"""
        engine = EnhancedMatchingEngine(self.bank_account_1.account, self.company)
        
        transaction = self.test_transactions[1]  # Transaction with different reference
        matches = engine.get_enhanced_matches(
            transaction,
            document_types=["payment_entry"]
        )
        
        # Should find partial match (amount + party)
        self.assertTrue(len(matches['partial_matches']) > 0)
        partial_match = matches['partial_matches'][0]
        self.assertGreaterEqual(partial_match['match_score'], 60)
        self.assertIn("Exact Amount", partial_match['match_details'])
    
    def test_internal_transfer_detection(self):
        """Test internal transfer detection"""
        manager = InternalTransferManager(self.bank_account_1.account, self.company)
        
        transaction = self.test_transactions[2]  # Withdrawal transaction
        result = manager.detect_internal_transfer(transaction)
        
        self.assertTrue(result['is_internal_transfer'])
        self.assertTrue(len(result['matching_transactions']) > 0)
        self.assertGreater(result['confidence'], 50)
        
        # Check matching transaction details
        matching_trans = result['matching_transactions'][0]
        self.assertEqual(matching_trans['target_bank_account'], self.bank_account_2.name)
    
    def test_internal_transfer_creation(self):
        """Test internal transfer payment entry creation"""
        from csf_tz.csftz_hooks.enhanced_bank_reconciliation import create_internal_transfer_payment_entry
        
        transaction = self.test_transactions[2]  # Withdrawal transaction
        
        result = create_internal_transfer_payment_entry(
            bank_transaction_name=transaction.name,
            target_bank_account=self.bank_account_2.name,
            auto_reconcile=True
        )
        
        self.assertEqual(result['status'], 'success')
        self.assertTrue(result['payment_entry'])
        
        # Verify payment entry was created
        pe = frappe.get_doc("Payment Entry", result['payment_entry'])
        self.assertEqual(pe.payment_type, "Internal Transfer")
        self.assertEqual(pe.paid_from, self.bank_account_1.account)
        self.assertEqual(pe.paid_to, self.bank_account_2.account)
    
    def test_filter_system_save_and_load(self):
        """Test filter save and load functionality"""
        filter_config = {
            "description_contains": "payment",
            "amount_from": 1000,
            "amount_to": 5000,
            "date_from": nowdate()
        }
        
        # Save filter
        filter_name = BankReconciliationFilters.save_filter(
            "Test Filter", filter_config, is_default=True
        )
        
        self.assertTrue(filter_name)
        
        # Load filters
        saved_filters = BankReconciliationFilters.get_saved_filters()
        test_filter = next((f for f in saved_filters if f.filter_name == "Test Filter"), None)
        
        self.assertIsNotNone(test_filter)
        self.assertTrue(test_filter.is_default)
        
        loaded_config = json.loads(test_filter.filter_config)
        self.assertEqual(loaded_config['description_contains'], "payment")
        self.assertEqual(loaded_config['amount_from'], 1000)
    
    def test_filter_application(self):
        """Test filter application to transactions"""
        transactions = [
            {
                'description': 'Payment from customer',
                'unallocated_amount': 1500,
                'date': nowdate()
            },
            {
                'description': 'Salary payment',
                'unallocated_amount': 3000,
                'date': add_days(nowdate(), -1)
            },
            {
                'description': 'Refund to customer',
                'unallocated_amount': 500,
                'date': nowdate()
            }
        ]
        
        filter_config = {
            "description_contains": "payment",
            "amount_from": 1000
        }
        
        filtered = BankReconciliationFilters.apply_filters(transactions, filter_config)
        
        # Should match first two transactions (contain "payment" and amount >= 1000)
        self.assertEqual(len(filtered), 2)
        self.assertIn("Payment from customer", [t['description'] for t in filtered])
        self.assertIn("Salary payment", [t['description'] for t in filtered])
    
    def test_edge_case_identical_references_different_amounts(self):
        """Test edge case: identical references with different amounts"""
        # Create transactions with same reference but different amounts
        trans1 = frappe.get_doc({
            "doctype": "Bank Transaction",
            "bank_account": self.bank_account_1,
            "date": nowdate(),
            "deposit": 1000.00,
            "reference_number": "SAME_REF",
            "description": "First payment"
        })
        trans1.insert()
        trans1.submit()
        
        trans2 = frappe.get_doc({
            "doctype": "Bank Transaction",
            "bank_account": self.bank_account_1,
            "date": nowdate(),
            "deposit": 2000.00,
            "reference_number": "SAME_REF",
            "description": "Second payment"
        })
        trans2.insert()
        trans2.submit()
        
        # Create payment entry
        pe = frappe.get_doc({
            "doctype": "Payment Entry",
            "payment_type": "Receive",
            "company": self.company,
            "posting_date": nowdate(),
            "paid_to": self.bank_account_1.account,
            "paid_amount": 1000.00,
            "received_amount": 1000.00,
            "reference_no": "SAME_REF"
        })
        pe.insert()
        pe.submit()
        
        # Test matching
        engine = EnhancedMatchingEngine(self.bank_account_1.account, self.company)
        
        # Should match with first transaction (exact amount + reference)
        matches1 = engine.get_enhanced_matches(trans1, document_types=["payment_entry"])
        matches2 = engine.get_enhanced_matches(trans2, document_types=["payment_entry"])
        
        # First transaction should have higher score (exact amount match)
        self.assertTrue(len(matches1['exact_matches']) > 0)
        self.assertTrue(len(matches2['partial_matches']) > 0)  # Reference match but not amount
    
    def test_edge_case_empty_references_description_match(self):
        """Test edge case: empty references but matching descriptions"""
        # Create transaction without reference
        trans = frappe.get_doc({
            "doctype": "Bank Transaction",
            "bank_account": self.bank_account_1,
            "date": nowdate(),
            "deposit": 1500.00,
            "description": "Customer payment for invoice INV-001"
        })
        trans.insert()
        trans.submit()
        
        # Create payment entry with similar description
        pe = frappe.get_doc({
            "doctype": "Payment Entry",
            "payment_type": "Receive",
            "company": self.company,
            "posting_date": nowdate(),
            "paid_to": self.bank_account_1.account,
            "paid_amount": 1500.00,
            "received_amount": 1500.00,
            "remarks": "Payment for invoice INV-001 from customer"
        })
        pe.insert()
        pe.submit()
        
        # Test matching
        engine = EnhancedMatchingEngine(self.bank_account_1.account, self.company)
        matches = engine.get_enhanced_matches(trans, document_types=["payment_entry"])
        
        # Should find match based on amount and description similarity
        total_matches = (len(matches['exact_matches']) + 
                        len(matches['partial_matches']) + 
                        len(matches['suggested_matches']))
        self.assertGreater(total_matches, 0)
    
    def test_edge_case_internal_transfer_one_leg_only(self):
        """Test edge case: internal transfer with only one leg available"""
        # Create only one side of internal transfer
        trans = frappe.get_doc({
            "doctype": "Bank Transaction",
            "bank_account": self.bank_account_1,
            "date": nowdate(),
            "withdrawal": 3000.00,
            "reference_number": "SINGLE_LEG",
            "description": "Transfer to other account"
        })
        trans.insert()
        trans.submit()
        
        # Test internal transfer detection
        manager = InternalTransferManager(self.bank_account_1.account, self.company)
        result = manager.detect_internal_transfer(trans)
        
        # Should not detect as internal transfer (no matching leg)
        self.assertFalse(result['is_internal_transfer'])
        self.assertEqual(len(result.get('matching_transactions', [])), 0)
    
    def tearDown(self):
        """Clean up test data"""
        # Delete test documents
        for trans in self.test_transactions:
            if frappe.db.exists("Bank Transaction", trans.name):
                frappe.delete_doc("Bank Transaction", trans.name, force=True)
        
        for pe in self.test_payment_entries:
            if frappe.db.exists("Payment Entry", pe.name):
                frappe.delete_doc("Payment Entry", pe.name, force=True)
        
        # Delete test filters
        test_filters = frappe.get_all(
            "Bank Reconciliation Filter",
            filters={"filter_name": ["like", "Test%"]}
        )
        for filter_doc in test_filters:
            frappe.delete_doc("Bank Reconciliation Filter", filter_doc.name, force=True)


def run_enhanced_bank_reconciliation_tests():
    """Run all enhanced bank reconciliation tests"""
    suite = unittest.TestLoader().loadTestsFromTestCase(TestEnhancedBankReconciliation)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    # Run tests when script is executed directly
    run_enhanced_bank_reconciliation_tests()
