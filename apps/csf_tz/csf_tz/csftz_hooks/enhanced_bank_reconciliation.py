"""
Enhanced Bank Reconciliation Tool for ERPNext
Provides improved matching logic, internal transfer support, and advanced filtering
"""

import frappe
import json
from frappe import _
from frappe.utils import cint, flt, getdate, nowdate
from erpnext.accounts.utils import get_account_currency
from erpnext.accounts.doctype.bank_reconciliation_tool.bank_reconciliation_tool import (
    get_linked_payments, reconcile_vouchers
)


class EnhancedMatchingEngine:
    """Enhanced matching engine with prioritized matching logic"""
    
    def __init__(self, bank_account, company):
        self.bank_account = bank_account
        self.company = company
        self.currency = get_account_currency(bank_account)
    
    def get_enhanced_matches(self, transaction, document_types=None, from_date=None, to_date=None,
                           filter_by_reference_date=None, from_reference_date=None, to_reference_date=None):
        """
        Enhanced matching with prioritized logic:
        1. Exact match: Reference number + Amount
        2. Partial match: Reference number OR Amount + Party
        3. Suggested match: Description similarity + Amount range
        """
        
        matches = {
            'exact_matches': [],
            'partial_matches': [],
            'suggested_matches': []
        }
        
        # Get all potential matches using existing logic
        potential_matches = get_linked_payments(
            transaction.name,
            document_types,
            from_date,
            to_date,
            filter_by_reference_date,
            from_reference_date,
            to_reference_date
        )
        
        for match in potential_matches:
            match_score = self._calculate_match_score(transaction, match)
            match['match_score'] = match_score
            match['match_type'] = self._determine_match_type(match_score)
            match['match_details'] = self._get_match_details(transaction, match)
            
            if match_score >= 90:
                matches['exact_matches'].append(match)
            elif match_score >= 60:
                matches['partial_matches'].append(match)
            else:
                matches['suggested_matches'].append(match)
        
        # Sort by match score
        for category in matches:
            matches[category].sort(key=lambda x: x['match_score'], reverse=True)
        
        return matches
    
    def _calculate_match_score(self, transaction, match):
        """Calculate match score based on multiple criteria"""
        score = 0
        
        # Reference number match (40 points)
        if (transaction.reference_number and match.get('reference_no') and 
            str(transaction.reference_number).strip().lower() == str(match.get('reference_no')).strip().lower()):
            score += 40
        
        # Exact amount match (30 points)
        if abs(flt(transaction.unallocated_amount) - flt(match.get('paid_amount', 0))) < 0.01:
            score += 30
        elif abs(flt(transaction.unallocated_amount) - flt(match.get('paid_amount', 0))) <= flt(transaction.unallocated_amount) * 0.05:
            # Amount within 5% tolerance (15 points)
            score += 15
        
        # Party match (15 points)
        if (transaction.party and match.get('party') and 
            transaction.party == match.get('party')):
            score += 15
        
        # Date proximity (10 points max)
        if transaction.date and match.get('posting_date'):
            date_diff = abs((getdate(transaction.date) - getdate(match.get('posting_date'))).days)
            if date_diff == 0:
                score += 10
            elif date_diff <= 3:
                score += 7
            elif date_diff <= 7:
                score += 5
            elif date_diff <= 30:
                score += 2
        
        # Description similarity (5 points)
        if self._check_description_similarity(transaction.description, match.get('remarks', '')):
            score += 5
        
        return min(score, 100)  # Cap at 100
    
    def _determine_match_type(self, score):
        """Determine match type based on score"""
        if score >= 90:
            return "Exact Match"
        elif score >= 60:
            return "Partial Match"
        else:
            return "Suggested Match"
    
    def _get_match_details(self, transaction, match):
        """Get detailed match information"""
        details = []
        
        if (transaction.reference_number and match.get('reference_no') and 
            str(transaction.reference_number).strip().lower() == str(match.get('reference_no')).strip().lower()):
            details.append("Reference Number")
        
        if abs(flt(transaction.unallocated_amount) - flt(match.get('paid_amount', 0))) < 0.01:
            details.append("Exact Amount")
        elif abs(flt(transaction.unallocated_amount) - flt(match.get('paid_amount', 0))) <= flt(transaction.unallocated_amount) * 0.05:
            details.append("Amount (~5%)")
        
        if (transaction.party and match.get('party') and transaction.party == match.get('party')):
            details.append("Party")
        
        if transaction.date and match.get('posting_date'):
            date_diff = abs((getdate(transaction.date) - getdate(match.get('posting_date'))).days)
            if date_diff <= 3:
                details.append("Date")
        
        return " + ".join(details) if details else "Low Confidence"
    
    def _check_description_similarity(self, desc1, desc2):
        """Check if descriptions have similar keywords"""
        if not desc1 or not desc2:
            return False
        
        # Simple keyword matching - can be enhanced with fuzzy matching
        words1 = set(desc1.lower().split())
        words2 = set(desc2.lower().split())
        
        # Remove common words
        common_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        words1 = words1 - common_words
        words2 = words2 - common_words
        
        if not words1 or not words2:
            return False
        
        # Check for intersection
        intersection = words1.intersection(words2)
        return len(intersection) >= min(2, min(len(words1), len(words2)) // 2)


class InternalTransferManager:
    """Manages internal transfer creation and reconciliation"""
    
    def __init__(self, bank_account, company):
        self.bank_account = bank_account
        self.company = company
    
    def detect_internal_transfer(self, transaction):
        """Detect if transaction is likely an internal transfer"""
        # Look for matching transaction in other company bank accounts
        matching_transactions = self._find_matching_internal_transactions(transaction)
        
        if matching_transactions:
            return {
                'is_internal_transfer': True,
                'matching_transactions': matching_transactions,
                'confidence': self._calculate_transfer_confidence(transaction, matching_transactions)
            }
        
        return {'is_internal_transfer': False}
    
    def _find_matching_internal_transactions(self, transaction):
        """Find matching transactions in other bank accounts"""
        # Get all bank accounts for the same company
        bank_accounts = frappe.get_all(
            "Bank Account",
            filters={"company": self.company, "name": ["!=", self.bank_account]},
            fields=["name", "account"]
        )
        
        matching_transactions = []
        
        for bank_acc in bank_accounts:
            # Look for opposite transactions (deposit vs withdrawal) with same amount and reference
            opposite_field = "withdrawal" if transaction.deposit > 0 else "deposit"
            
            matches = frappe.get_all(
                "Bank Transaction",
                filters={
                    "bank_account": bank_acc.name,
                    opposite_field: transaction.unallocated_amount,
                    "date": ["between", [
                        frappe.utils.add_days(transaction.date, -3),
                        frappe.utils.add_days(transaction.date, 3)
                    ]],
                    "docstatus": 1,
                    "unallocated_amount": [">", 0]
                },
                fields=["name", "reference_number", "date", "description", opposite_field]
            )
            
            for match in matches:
                # Additional validation
                if self._validate_internal_transfer_match(transaction, match):
                    match['target_bank_account'] = bank_acc.name
                    matching_transactions.append(match)
        
        return matching_transactions
    
    def _validate_internal_transfer_match(self, transaction, potential_match):
        """Validate if two transactions are likely internal transfers"""
        # Reference number match
        if (transaction.reference_number and potential_match.reference_number and
            transaction.reference_number == potential_match.reference_number):
            return True
        
        # Description similarity for internal transfers
        if self._check_internal_transfer_description(transaction.description, potential_match.description):
            return True
        
        return False
    
    def _check_internal_transfer_description(self, desc1, desc2):
        """Check if descriptions indicate internal transfer"""
        internal_keywords = ['transfer', 'internal', 'between', 'from', 'to', 'account']
        
        desc1_lower = desc1.lower() if desc1 else ""
        desc2_lower = desc2.lower() if desc2 else ""
        
        # Check for internal transfer keywords
        for keyword in internal_keywords:
            if keyword in desc1_lower or keyword in desc2_lower:
                return True
        
        return False
    
    def _calculate_transfer_confidence(self, transaction, matching_transactions):
        """Calculate confidence level for internal transfer detection"""
        if not matching_transactions:
            return 0
        
        max_confidence = 0
        for match in matching_transactions:
            confidence = 0
            
            # Reference number match
            if (transaction.reference_number and match.reference_number and
                transaction.reference_number == match.reference_number):
                confidence += 60
            
            # Date proximity
            date_diff = abs((getdate(transaction.date) - getdate(match.date)).days)
            if date_diff == 0:
                confidence += 30
            elif date_diff <= 1:
                confidence += 20
            elif date_diff <= 3:
                confidence += 10
            
            # Description similarity
            if self._check_internal_transfer_description(transaction.description, match.description):
                confidence += 10
            
            max_confidence = max(max_confidence, confidence)
        
        return max_confidence


@frappe.whitelist()
def get_enhanced_linked_payments(
    bank_transaction_name,
    document_types=None,
    from_date=None,
    to_date=None,
    filter_by_reference_date=None,
    from_reference_date=None,
    to_reference_date=None,
    use_enhanced_matching=True
):
    """Enhanced version of get_linked_payments with improved matching"""
    
    if not use_enhanced_matching:
        # Fall back to original logic
        return get_linked_payments(
            bank_transaction_name, document_types, from_date, to_date,
            filter_by_reference_date, from_reference_date, to_reference_date
        )
    
    # Get bank transaction
    transaction = frappe.get_doc("Bank Transaction", bank_transaction_name)
    bank_account_doc = frappe.get_doc("Bank Account", transaction.bank_account)
    
    # Initialize enhanced matching engine
    matching_engine = EnhancedMatchingEngine(bank_account_doc.account, bank_account_doc.company)
    
    # Get enhanced matches
    enhanced_matches = matching_engine.get_enhanced_matches(
        transaction, document_types, from_date, to_date,
        filter_by_reference_date, from_reference_date, to_reference_date
    )
    
    # Check for internal transfers
    transfer_manager = InternalTransferManager(bank_account_doc.account, bank_account_doc.company)
    internal_transfer_info = transfer_manager.detect_internal_transfer(transaction)
    
    return {
        'matches': enhanced_matches,
        'internal_transfer': internal_transfer_info,
        'transaction_info': {
            'name': transaction.name,
            'amount': transaction.unallocated_amount,
            'date': transaction.date,
            'reference_number': transaction.reference_number,
            'description': transaction.description
        }
    }


@frappe.whitelist()
def create_internal_transfer_payment_entry(
    bank_transaction_name,
    target_bank_account,
    reference_number=None,
    reference_date=None,
    posting_date=None,
    remarks=None,
    auto_reconcile=True
):
    """Create internal transfer payment entry"""

    # Get bank transaction
    transaction = frappe.get_doc("Bank Transaction", bank_transaction_name)
    source_bank_account = frappe.get_doc("Bank Account", transaction.bank_account)
    target_bank_account_doc = frappe.get_doc("Bank Account", target_bank_account)

    # Validate same company
    if source_bank_account.company != target_bank_account_doc.company:
        frappe.throw(_("Internal transfers can only be made between accounts of the same company"))

    # Validate different accounts
    if source_bank_account.name == target_bank_account:
        frappe.throw(_("Source and target bank accounts must be different"))

    # Create Payment Entry
    pe = frappe.new_doc("Payment Entry")
    pe.payment_type = "Internal Transfer"
    pe.company = source_bank_account.company
    pe.posting_date = posting_date or transaction.date
    pe.reference_no = reference_number or transaction.reference_number
    pe.reference_date = reference_date or transaction.date

    # Set accounts based on transaction type
    if transaction.deposit > 0:
        # Money coming into source account - transfer FROM target TO source
        pe.paid_from = target_bank_account_doc.account
        pe.paid_to = source_bank_account.account
        pe.paid_amount = transaction.unallocated_amount
        pe.received_amount = transaction.unallocated_amount
    else:
        # Money going out of source account - transfer FROM source TO target
        pe.paid_from = source_bank_account.account
        pe.paid_to = target_bank_account_doc.account
        pe.paid_amount = abs(transaction.unallocated_amount)
        pe.received_amount = abs(transaction.unallocated_amount)

    pe.paid_from_account_currency = get_account_currency(pe.paid_from)
    pe.paid_to_account_currency = get_account_currency(pe.paid_to)

    # Set exchange rate if different currencies
    if pe.paid_from_account_currency != pe.paid_to_account_currency:
        from erpnext.setup.utils import get_exchange_rate
        pe.source_exchange_rate = get_exchange_rate(
            pe.paid_from_account_currency, pe.company_currency, pe.posting_date
        )
        pe.target_exchange_rate = get_exchange_rate(
            pe.paid_to_account_currency, pe.company_currency, pe.posting_date
        )
    else:
        pe.source_exchange_rate = 1.0
        pe.target_exchange_rate = 1.0

    pe.remarks = remarks or f"Internal Transfer - {transaction.description}"

    # Save and submit
    pe.insert()
    pe.submit()

    # Reconcile with bank transaction
    vouchers = json.dumps([{
        "payment_doctype": "Payment Entry",
        "payment_name": pe.name,
        "amount": pe.paid_amount if transaction.withdrawal > 0 else pe.received_amount,
    }])

    reconciled_transaction = reconcile_vouchers(bank_transaction_name, vouchers)

    # Auto-reconcile matching transaction if requested
    if auto_reconcile:
        matching_transaction = _find_and_reconcile_matching_transaction(
            pe, target_bank_account, transaction
        )

        return {
            'payment_entry': pe.name,
            'reconciled_transaction': reconciled_transaction.name,
            'matching_transaction': matching_transaction,
            'status': 'success'
        }

    return {
        'payment_entry': pe.name,
        'reconciled_transaction': reconciled_transaction.name,
        'status': 'success'
    }


def _find_and_reconcile_matching_transaction(payment_entry, target_bank_account, source_transaction):
    """Find and reconcile matching transaction in target bank account"""

    # Look for matching transaction
    opposite_field = "deposit" if source_transaction.withdrawal > 0 else "withdrawal"

    matching_transactions = frappe.get_all(
        "Bank Transaction",
        filters={
            "bank_account": target_bank_account,
            opposite_field: abs(source_transaction.unallocated_amount),
            "date": ["between", [
                frappe.utils.add_days(source_transaction.date, -3),
                frappe.utils.add_days(source_transaction.date, 3)
            ]],
            "docstatus": 1,
            "unallocated_amount": [">", 0]
        },
        fields=["name", "reference_number", "description"]
    )

    # Find best match
    best_match = None
    for match in matching_transactions:
        if (source_transaction.reference_number and match.reference_number and
            source_transaction.reference_number == match.reference_number):
            best_match = match
            break

    if not best_match and matching_transactions:
        # Take first match if no reference number match
        best_match = matching_transactions[0]

    if best_match:
        # Create voucher for matching transaction
        vouchers = json.dumps([{
            "payment_doctype": "Payment Entry",
            "payment_name": payment_entry.name,
            "amount": abs(source_transaction.unallocated_amount),
        }])

        try:
            reconciled_match = reconcile_vouchers(best_match.name, vouchers)
            return {
                'name': best_match.name,
                'status': 'reconciled',
                'reconciled_transaction': reconciled_match.name
            }
        except Exception as e:
            frappe.log_error(f"Error reconciling matching transaction: {str(e)}")
            return {
                'name': best_match.name,
                'status': 'found_but_not_reconciled',
                'error': str(e)
            }

    return None


class BankReconciliationFilters:
    """Advanced filtering system for bank reconciliation"""

    @staticmethod
    def get_saved_filters(user=None):
        """Get saved filters for user"""
        if not user:
            user = frappe.session.user

        filters = frappe.get_all(
            "Bank Reconciliation Filter",
            filters={"owner": user},
            fields=["name", "filter_name", "filter_config", "is_default"],
            order_by="creation desc"
        )

        return filters

    @staticmethod
    def save_filter(filter_name, filter_config, is_default=False, user=None):
        """Save filter configuration"""
        if not user:
            user = frappe.session.user

        # Check if filter with same name exists
        existing = frappe.db.exists("Bank Reconciliation Filter", {
            "filter_name": filter_name,
            "owner": user
        })

        if existing:
            # Update existing
            doc = frappe.get_doc("Bank Reconciliation Filter", existing)
            doc.filter_config = json.dumps(filter_config)
            doc.is_default = is_default
            doc.save()
        else:
            # Create new
            doc = frappe.new_doc("Bank Reconciliation Filter")
            doc.filter_name = filter_name
            doc.filter_config = json.dumps(filter_config)
            doc.is_default = is_default
            doc.insert()

        # If this is default, unset other defaults
        if is_default:
            frappe.db.sql("""
                UPDATE `tabBank Reconciliation Filter`
                SET is_default = 0
                WHERE owner = %s AND name != %s
            """, (user, doc.name))

        return doc.name

    @staticmethod
    def delete_filter(filter_name, user=None):
        """Delete saved filter"""
        if not user:
            user = frappe.session.user

        filter_doc = frappe.db.exists("Bank Reconciliation Filter", {
            "filter_name": filter_name,
            "owner": user
        })

        if filter_doc:
            frappe.delete_doc("Bank Reconciliation Filter", filter_doc)
            return True

        return False

    @staticmethod
    def apply_filters(transactions, filter_config):
        """Apply filters to transaction list"""
        if not filter_config:
            return transactions

        filtered_transactions = []

        for transaction in transactions:
            if BankReconciliationFilters._transaction_matches_filter(transaction, filter_config):
                filtered_transactions.append(transaction)

        return filtered_transactions

    @staticmethod
    def _transaction_matches_filter(transaction, filter_config):
        """Check if transaction matches filter criteria"""

        # Description filter
        if filter_config.get('description_contains'):
            desc_filter = filter_config['description_contains'].lower()
            transaction_desc = (transaction.get('description') or '').lower()

            if filter_config.get('description_is_regex'):
                import re
                try:
                    if not re.search(desc_filter, transaction_desc):
                        return False
                except re.error:
                    # Invalid regex, treat as plain text
                    if desc_filter not in transaction_desc:
                        return False
            else:
                if desc_filter not in transaction_desc:
                    return False

        # Reference number filter
        if filter_config.get('reference_number'):
            ref_filter = filter_config['reference_number'].lower()
            transaction_ref = (transaction.get('reference_number') or '').lower()

            if filter_config.get('reference_exact_match'):
                if ref_filter != transaction_ref:
                    return False
            else:
                if ref_filter not in transaction_ref:
                    return False

        # Date range filter
        if filter_config.get('date_from') or filter_config.get('date_to'):
            transaction_date = getdate(transaction.get('date'))

            if filter_config.get('date_from'):
                if transaction_date < getdate(filter_config['date_from']):
                    return False

            if filter_config.get('date_to'):
                if transaction_date > getdate(filter_config['date_to']):
                    return False

        # Amount range filter
        if filter_config.get('amount_from') is not None or filter_config.get('amount_to') is not None:
            transaction_amount = abs(flt(transaction.get('unallocated_amount', 0)))

            if filter_config.get('amount_from') is not None:
                if transaction_amount < flt(filter_config['amount_from']):
                    return False

            if filter_config.get('amount_to') is not None:
                if transaction_amount > flt(filter_config['amount_to']):
                    return False

        # Status filter
        if filter_config.get('status'):
            # This would need to be implemented based on transaction status logic
            pass

        return True


@frappe.whitelist()
def get_bank_reconciliation_filters(user=None):
    """API endpoint to get saved filters"""
    return BankReconciliationFilters.get_saved_filters(user)


@frappe.whitelist()
def save_bank_reconciliation_filter(filter_name, filter_config, is_default=False, user=None):
    """API endpoint to save filter"""
    if isinstance(filter_config, str):
        filter_config = json.loads(filter_config)

    return BankReconciliationFilters.save_filter(filter_name, filter_config, is_default, user)


@frappe.whitelist()
def delete_bank_reconciliation_filter(filter_name, user=None):
    """API endpoint to delete filter"""
    return BankReconciliationFilters.delete_filter(filter_name, user)


@frappe.whitelist()
def detect_internal_transfers(source_bank_account, target_bank_account, from_date=None, to_date=None):
    """API endpoint to detect internal transfers between bank accounts"""

    # Get source bank account details
    source_bank_doc = frappe.get_doc("Bank Account", source_bank_account)
    target_bank_doc = frappe.get_doc("Bank Account", target_bank_account)

    # Validate same company
    if source_bank_doc.company != target_bank_doc.company:
        frappe.throw(_("Both bank accounts must belong to the same company"))

    # Get unreconciled transactions from source account
    source_transactions = frappe.get_all(
        "Bank Transaction",
        filters={
            "bank_account": source_bank_account,
            "docstatus": 1,
            "unallocated_amount": [">", 0],
            "date": ["between", [from_date or "1900-01-01", to_date or "2100-12-31"]]
        },
        fields=["name", "date", "deposit", "withdrawal", "reference_number", "description", "unallocated_amount"]
    )

    potential_transfers = []

    for trans in source_transactions:
        # Initialize transfer manager
        transfer_manager = InternalTransferManager(source_bank_doc.account, source_bank_doc.company)

        # Create a mock transaction object
        transaction_obj = frappe._dict({
            'name': trans.name,
            'date': trans.date,
            'deposit': trans.deposit or 0,
            'withdrawal': trans.withdrawal or 0,
            'reference_number': trans.reference_number,
            'description': trans.description,
            'unallocated_amount': trans.unallocated_amount
        })

        # Detect internal transfer
        result = transfer_manager.detect_internal_transfer(transaction_obj)

        if result['is_internal_transfer']:
            # Filter for the specific target account
            matching_in_target = [
                match for match in result['matching_transactions']
                if match['target_bank_account'] == target_bank_account
            ]

            if matching_in_target:
                potential_transfers.append({
                    'transaction_name': trans.name,
                    'amount': trans.unallocated_amount,
                    'date': trans.date,
                    'reference_number': trans.reference_number,
                    'description': trans.description,
                    'target_account': target_bank_account,
                    'confidence': result['confidence'],
                    'matching_transactions': matching_in_target
                })

    return potential_transfers


def get_enhanced_matching_queries(
    bank_account,
    company,
    transaction,
    document_types=None,
    exact_match=None,
    account_from_to=None,
    from_date=None,
    to_date=None,
    filter_by_reference_date=None,
    from_reference_date=None,
    to_reference_date=None,
    common_filters=None,
):
    """
    Enhanced matching queries hook for bank reconciliation
    This function is called by ERPNext's bank reconciliation system
    """
    queries = []

    # Only add enhanced queries if enhanced matching is enabled
    enhanced_matching_enabled = frappe.db.get_single_value(
        "Bank Reconciliation Settings", "enable_enhanced_matching"
    )

    if not enhanced_matching_enabled:
        return queries

    # Initialize enhanced matching engine
    matching_engine = EnhancedMatchingEngine(bank_account, company)

    # Get enhanced matches and convert to query format
    enhanced_matches = matching_engine.get_enhanced_matches(
        transaction, document_types, from_date, to_date,
        filter_by_reference_date, from_reference_date, to_reference_date
    )

    # Convert enhanced matches to query format expected by ERPNext
    # This is a simplified implementation - in practice, you might want to
    # integrate more deeply with the existing query system

    return queries
