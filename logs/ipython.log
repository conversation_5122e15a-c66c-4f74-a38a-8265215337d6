2025-01-25 11:57:39,143 INFO ipython === bench console session ===
2025-01-25 11:57:39,145 INFO ipython filters = {"bl_no": "BL123"}
get_data(filters=filters)
2025-01-25 11:57:39,146 INFO ipython === session end ===
2025-02-20 17:50:11,477 INFO ipython === bench console session ===
2025-02-20 17:50:11,477 INFO ipython account = frappe.db.get_value("Account", "Debtors - ITL", ["root_type", "account_type"])
print(account)
account = frappe.db.get_value("Account", "Debtors - ITL", ["root_type", "account_type"])
print(account)
2025-02-20 17:50:11,478 INFO ipython === session end ===
2025-02-26 12:04:42,132 INFO ipython === bench console session ===
2025-02-26 12:04:42,133 INFO ipython values = frappe.db.get_values("Property Setter")
print(values)
2025-02-26 12:04:42,134 INFO ipython === session end ===
2025-04-18 15:02:33,243 INFO ipython === bench console session ===
2025-04-18 15:02:33,244 INFO ipython # List all Custom DocPerm entries for the role
frappe.get_all("Custom DocPerm", filters={"role": "Test Custom Role"})
2025-04-18 15:02:33,244 INFO ipython # In Frappe Bench Console
frappe.get_doc("DocPerm", {"parent": "Customer", "role": "Test Custom Role"})
2025-04-18 15:02:33,244 INFO ipython # In Frappe Bench Console
frappe.get_doc("Custom DocPerm", {"parent": "Customer", "role": "Test Custom Role"})
2025-04-18 15:02:33,245 INFO ipython # In Frappe Bench Console
custom_docperm = frappe.get_all(
    "Custom DocPerm",
    filters={"parent": "Customer", "role": "Test Custom Role"},
    fields=["*"]
)
print(custom_docperm)  # Should return entries if auto-permission worked
2025-04-18 15:02:33,245 INFO ipython # In Frappe Bench Console
custom_docperm = frappe.get_all(
    "Custom DocPerm",
    filters={"parent": "Sales Invoices", "role": "Test Custom Role"},
    fields=["*"]
)
print(custom_docperm)  # Should return entries if auto-permission worked
2025-04-18 15:02:33,246 INFO ipython # In Frappe Bench Console
custom_docperm = frappe.get_all(
    "Custom DocPerm",
    filters={"parent": "Sales Invoice", "role": "Test Custom Role"},
    fields=["*"]
)
print(custom_docperm)  # Should return entries if auto-permission worked
2025-04-18 15:02:33,246 INFO ipython # In Frappe Bench Console
custom_docperm = frappe.get_all(
    "Custom DocPerm",
    filters={"parent": "Asset", "role": "Test Custom Role"},
    fields=["*"]
)
print(custom_docperm)  # Should return entries if auto-permission worked
2025-04-18 15:02:33,247 INFO ipython === session end ===
2025-04-18 21:18:04,919 INFO ipython === bench console session ===
2025-04-18 21:18:04,920 INFO ipython # In Frappe Bench Console
frappe.get_all("DocPerm", {"parent": "Customer", "role": "Accounts User", "select": 1})
2025-04-18 21:18:04,921 INFO ipython # In Frappe Bench Console
frappe.get_all("DocPerm", {"parent": "Sales Invoice", "role": "Accounts User", "select": 1})
2025-04-18 21:18:04,921 INFO ipython # In Frappe Bench Console
frappe.get_all("DocPerm", {"parent": "Sales Invoice", "role": "Accounts User", "select": 0})
2025-04-18 21:18:04,922 INFO ipython frappe.get_doc("DocPerm", {"parent": "Customer", "role": "Accounts User", "read": 1})
2025-04-18 21:18:04,922 INFO ipython === session end ===
2025-05-08 00:53:08,195 INFO ipython === bench console session ===
2025-05-08 00:53:08,196 INFO ipython frappe.get_doc("Container", "ICD-C-2025-00014").__dict__
2025-05-08 00:53:08,196 INFO ipython === session end ===
2025-06-05 17:49:56,904 INFO ipython === bench console session ===
2025-06-05 17:49:56,905 INFO ipython import frappe
2025-06-05 17:49:56,905 INFO ipython frappe.get_hooks("doc_events")
2025-06-05 17:49:56,905 INFO ipython from icd_tz.icd_tz.api.payment_entry import clear_paid_invoice_references
2025-06-05 17:49:56,905 INFO ipython print("Import successful!")
2025-06-05 17:49:56,905 INFO ipython === session end ===
2025-06-05 17:53:42,837 INFO ipython === bench console session ===
2025-06-05 17:53:42,838 INFO ipython from icd_tz.icd_tz.api.payment_entry import clear_invoice_references_from_charges
2025-06-05 17:53:42,838 INFO ipython print("Import successful!")
2025-06-05 17:53:42,838 INFO ipython settings = frappe.get_doc("ICD TZ Settings")
2025-06-05 17:53:42,839 INFO ipython print(f"Found {len(settings.service_types)} service types")
2025-06-05 17:53:42,839 INFO ipython for row in settings.service_types[:3]:
        print(f"Service: {row.service_name}, Type: {row.service_type}")
        
2025-06-05 17:53:42,839 INFO ipython === session end ===
2025-06-05 18:01:53,309 INFO ipython === bench console session ===
2025-06-05 18:01:53,311 INFO ipython # Let's check if there are any Sales Invoices with container_id populated
2025-06-05 18:01:53,312 INFO ipython sales_invoices = frappe.db.sql("""
    SELECT si.name, si.m_bl_no, sii.container_id, sii.item_code
        FROM `tabSales Invoice` si
            JOIN `tabSales Invoice Item` sii ON si.name = sii.parent
                WHERE si.m_bl_no IS NOT NULL AND si.m_bl_no != ''
                    LIMIT 5
                    """, as_dict=True)
2025-06-05 18:01:53,312 INFO ipython for invoice in sales_invoices:
        print(f"Invoice: {invoice.name}, M_BL: {invoice.m_bl_no}, Container ID: {invoice.container_id}, Item: {invoice.item_code}")
        
2025-06-05 18:01:53,312 INFO ipython # Let's check a specific container to see if invoice references are set
2025-06-05 18:01:53,312 INFO ipython container_id = "ICD-C-2025-00001"
2025-06-05 18:01:53,312 INFO ipython container = frappe.get_doc("Container", container_id)
2025-06-05 18:01:53,313 INFO ipython print(f"Container: {container.name}")
2025-06-05 18:01:53,313 INFO ipython print(f"Removal charges: {container.has_removal_charges}, Invoice: {container.r_sales_invoice}")
2025-06-05 18:01:53,313 INFO ipython print(f"Corridor charges: {container.has_corridor_levy_charges}, Invoice: {container.c_sales_invoice}")
2025-06-05 18:01:53,313 INFO ipython print(f"Days to be billed: {container.days_to_be_billed}")
2025-06-05 18:01:53,313 INFO ipython # Check container reception
2025-06-05 18:01:53,313 INFO ipython if container.container_reception:
        reception = frappe.get_doc("Container Reception", container.container_reception)
            print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:01:53,313 INFO ipython     print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:01:53,314 INFO ipython if container.container_reception:
        reception = frappe.get_doc("Container Reception", container.container_reception)
            print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:01:53,314 INFO ipython     print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:01:53,314 INFO ipython else:
        print("No container reception found")
2025-06-05 18:01:53,314 INFO ipython === session end ===
2025-06-05 18:07:47,415 INFO ipython === bench console session ===
2025-06-05 18:07:47,415 INFO ipython # Let's test the sales invoice hook manually
2025-06-05 18:07:47,415 INFO ipython invoice_name = "ACC-SINV-2025-01669"
2025-06-05 18:07:47,416 INFO ipython from icd_tz.icd_tz.api.sales_invoice import update_sales_references
2025-06-05 18:07:47,416 INFO ipython # Get the invoice
2025-06-05 18:07:47,416 INFO ipython invoice_doc = frappe.get_doc("Sales Invoice", invoice_name)
2025-06-05 18:07:47,416 INFO ipython print(f"Invoice: {invoice_doc.name}, M_BL: {invoice_doc.m_bl_no}")
2025-06-05 18:07:47,416 INFO ipython print(f"Items count: {len(invoice_doc.items)}")
2025-06-05 18:07:47,416 INFO ipython # Test the update function
2025-06-05 18:07:47,416 INFO ipython update_sales_references(invoice_doc)
2025-06-05 18:07:47,417 INFO ipython # Check if the container references were updated
2025-06-05 18:07:47,417 INFO ipython container_id = "ICD-C-2025-00001"
2025-06-05 18:07:47,417 INFO ipython container = frappe.get_doc("Container", container_id)
2025-06-05 18:07:47,417 INFO ipython print(f"After update:")
2025-06-05 18:07:47,417 INFO ipython print(f"Removal charges: {container.has_removal_charges}, Invoice: {container.r_sales_invoice}")
2025-06-05 18:07:47,417 INFO ipython print(f"Corridor charges: {container.has_corridor_levy_charges}, Invoice: {container.c_sales_invoice}")
2025-06-05 18:07:47,417 INFO ipython # Check container reception
2025-06-05 18:07:47,417 INFO ipython if container.container_reception:
        reception = frappe.get_doc("Container Reception", container.container_reception)
            print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:07:47,417 INFO ipython     print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:07:47,417 INFO ipython else:
        print("No container reception found")
2025-06-05 18:07:47,418 INFO ipython if container.container_reception:
        reception = frappe.get_doc("Container Reception", container.container_reception)
            print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:07:47,418 INFO ipython     print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:07:47,418 INFO ipython else:
        print("No container reception found")
2025-06-05 18:07:47,418 INFO ipython reception = frappe.get_doc("Container Reception", container.container_reception)
2025-06-05 18:07:47,418 INFO ipython print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:07:47,418 INFO ipython print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:07:47,418 INFO ipython # Let's check what the current status should be for Gate Pass validation
2025-06-05 18:07:47,418 INFO ipython print("=== Current Container Status ===")
2025-06-05 18:07:47,419 INFO ipython print(f"Container: {container.name}")
2025-06-05 18:07:47,419 INFO ipython print(f"Days to be billed: {container.days_to_be_billed}")
2025-06-05 18:07:47,419 INFO ipython print(f"Has removal charges: {container.has_removal_charges}")
2025-06-05 18:07:47,419 INFO ipython print(f"Has corridor charges: {container.has_corridor_levy_charges}")
2025-06-05 18:07:47,419 INFO ipython print(f"Removal invoice: {container.r_sales_invoice}")
2025-06-05 18:07:47,419 INFO ipython print(f"Corridor invoice: {container.c_sales_invoice}")
2025-06-05 18:07:47,419 INFO ipython print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:07:47,419 INFO ipython print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:07:47,419 INFO ipython print("\n=== Gate Pass Validation Logic ===")
2025-06-05 18:07:47,420 INFO ipython print("1. Storage charges pending:", container.days_to_be_billed > 0)
2025-06-05 18:07:47,420 INFO ipython print("2. Removal charges pending:", container.has_removal_charges == "Yes" and not container.r_sales_invoice)
2025-06-05 18:07:47,420 INFO ipython print("3. Corridor charges pending:", container.has_corridor_levy_charges == "Yes" and not container.c_sales_invoice)
2025-06-05 18:07:47,420 INFO ipython # Check the invoice payment status
2025-06-05 18:07:47,420 INFO ipython print(f"Invoice outstanding amount: {invoice_doc.outstanding_amount}")
2025-06-05 18:07:47,420 INFO ipython print(f"Invoice status: {invoice_doc.status}")
2025-06-05 18:07:47,420 INFO ipython print(f"Invoice grand total: {invoice_doc.grand_total}")
2025-06-05 18:07:47,420 INFO ipython # This container should NOT have pending payments according to the validation logic
2025-06-05 18:07:47,420 INFO ipython print("\n=== CONCLUSION ===")
2025-06-05 18:07:47,421 INFO ipython print("All validation checks are FALSE, so there should be NO pending payments error!")
2025-06-05 18:07:47,421 INFO ipython === session end ===
2025-06-05 18:18:23,384 INFO ipython === bench console session ===
2025-06-05 18:18:23,386 INFO ipython # Test the clear function on the cancelled invoice
2025-06-05 18:18:23,386 INFO ipython invoice_name = "ACC-SINV-2025-01669"
2025-06-05 18:18:23,386 INFO ipython from icd_tz.icd_tz.api.sales_invoice import clear_sales_references
2025-06-05 18:18:23,386 INFO ipython # Get the cancelled invoice
2025-06-05 18:18:23,386 INFO ipython invoice_doc = frappe.get_doc("Sales Invoice", invoice_name)
2025-06-05 18:18:23,386 INFO ipython print(f"Invoice: {invoice_doc.name}, Status: {invoice_doc.status}")
2025-06-05 18:18:23,387 INFO ipython # Clear the references
2025-06-05 18:18:23,387 INFO ipython clear_sales_references(invoice_doc)
2025-06-05 18:18:23,387 INFO ipython print("References cleared!")
2025-06-05 18:18:23,387 INFO ipython # Check if the container references were cleared
2025-06-05 18:18:23,387 INFO ipython container_id = "ICD-C-2025-00001"
2025-06-05 18:18:23,387 INFO ipython container = frappe.get_doc("Container", container_id)
2025-06-05 18:18:23,387 INFO ipython print(f"=== After clearing references ===")
2025-06-05 18:18:23,387 INFO ipython print(f"Removal charges: {container.has_removal_charges}, Invoice: {container.r_sales_invoice}")
2025-06-05 18:18:23,388 INFO ipython print(f"Corridor charges: {container.has_corridor_levy_charges}, Invoice: {container.c_sales_invoice}")
2025-06-05 18:18:23,388 INFO ipython # Check container reception
2025-06-05 18:18:23,388 INFO ipython reception = frappe.get_doc("Container Reception", container.container_reception)
2025-06-05 18:18:23,388 INFO ipython print(f"Transport invoice: {reception.t_sales_invoice}")
2025-06-05 18:18:23,388 INFO ipython print(f"Shore invoice: {reception.s_sales_invoice}")
2025-06-05 18:18:23,388 INFO ipython print("\n=== Gate Pass Validation Logic ===")
2025-06-05 18:18:23,388 INFO ipython print("1. Storage charges pending:", container.days_to_be_billed > 0)
2025-06-05 18:18:23,388 INFO ipython print("2. Removal charges pending:", container.has_removal_charges == "Yes" and not container.r_sales_invoice)
2025-06-05 18:18:23,389 INFO ipython print("3. Corridor charges pending:", container.has_corridor_levy_charges == "Yes" and not container.c_sales_invoice)
2025-06-05 18:18:23,389 INFO ipython === session end ===
2025-06-05 22:14:29,805 INFO ipython === bench console session ===
2025-06-05 22:14:29,806 INFO ipython # Let's investigate the real issue - check for ACTIVE invoices with outstanding amounts
2025-06-05 22:14:29,806 INFO ipython container_id = "ICD-C-2025-00001"
2025-06-05 22:14:29,806 INFO ipython # Check for submitted invoices (not cancelled) with this container
2025-06-05 22:14:29,806 INFO ipython active_invoices = frappe.db.sql("""
    SELECT si.name, si.status, si.outstanding_amount, si.grand_total, sii.item_code
        FROM `tabSales Invoice` si
            JOIN `tabSales Invoice Item` sii ON si.name = sii.parent
                WHERE sii.container_id = %s 
                    AND si.docstatus = 1 
                        AND si.status != 'Cancelled'
                            ORDER BY si.creation DESC
                            """, (container_id,), as_dict=True)
2025-06-05 22:14:29,806 INFO ipython print(f"=== ACTIVE INVOICES for Container {container_id} ===")
2025-06-05 22:14:29,807 INFO ipython if not active_invoices:
        print("No active invoices found!")
        else:
                for invoice in active_invoices:
                            print(f"Invoice: {invoice.name}")
2025-06-05 22:14:29,807 INFO ipython         print(f"  Status: {invoice.status}")
2025-06-05 22:14:29,807 INFO ipython         print(f"  Outstanding: {invoice.outstanding_amount}")
2025-06-05 22:14:29,807 INFO ipython         print(f"  Grand Total: {invoice.grand_total}")
2025-06-05 22:14:29,807 INFO ipython         print(f"  Item: {invoice.item_code}")
2025-06-05 22:14:29,807 INFO ipython         print(f"  Paid: {'Yes' if invoice.outstanding_amount == 0 else 'No'}")
2025-06-05 22:14:29,808 INFO ipython         print("---")
2025-06-05 22:14:29,808 INFO ipython print(f"Number of active invoices: {len(active_invoices)}")
2025-06-05 22:14:29,808 INFO ipython for i, invoice in enumerate(active_invoices):
        print(f"{i+1}. Invoice: {invoice.name}, Status: {invoice.status}, Outstanding: {invoice.outstanding_amount}, Item: {invoice.item_code}")
        
2025-06-05 22:14:29,808 INFO ipython # Check current container references
2025-06-05 22:14:29,808 INFO ipython container = frappe.get_doc("Container", container_id)
2025-06-05 22:14:29,808 INFO ipython reception = frappe.get_doc("Container Reception", container.container_reception)
2025-06-05 22:14:29,808 INFO ipython print("=== CURRENT CONTAINER REFERENCES ===")
2025-06-05 22:14:29,808 INFO ipython print(f"Container corridor invoice: {container.c_sales_invoice}")
2025-06-05 22:14:29,808 INFO ipython print(f"Container removal invoice: {container.r_sales_invoice}")
2025-06-05 22:14:29,809 INFO ipython print(f"Reception transport invoice: {reception.t_sales_invoice}")
2025-06-05 22:14:29,809 INFO ipython print(f"Reception shore invoice: {reception.s_sales_invoice}")
2025-06-05 22:14:29,809 INFO ipython print("\n=== SHOULD BE POINTING TO ===")
2025-06-05 22:14:29,809 INFO ipython print(f"New PAID invoice: ACC-SINV-2025-01670")
2025-06-05 22:14:29,809 INFO ipython print("\n=== THE SOLUTION ===")
2025-06-05 22:14:29,809 INFO ipython print("The invoice references need to be updated to point to the new PAID invoice!")
2025-06-05 22:14:29,809 INFO ipython print("This is why the Gate Pass validation fails - it's checking against the old cancelled invoice.")
2025-06-05 22:14:29,809 INFO ipython # Fix the missing corridor invoice reference
2025-06-05 22:14:29,809 INFO ipython print("=== FIXING THE ISSUE ===")
2025-06-05 22:14:29,809 INFO ipython print(f"Setting corridor invoice to: ACC-SINV-2025-01670")
2025-06-05 22:14:29,809 INFO ipython # Update the corridor invoice reference
2025-06-05 22:14:29,809 INFO ipython container.c_sales_invoice = "ACC-SINV-2025-01670"
2025-06-05 22:14:29,810 INFO ipython container.save(ignore_permissions=True)
2025-06-05 22:14:29,810 INFO ipython print("Container updated!")
2025-06-05 22:14:29,810 INFO ipython # Verify the fix
2025-06-05 22:14:29,810 INFO ipython container.reload()
2025-06-05 22:14:29,810 INFO ipython print(f"\nAfter fix:")
2025-06-05 22:14:29,810 INFO ipython print(f"Corridor charges: {container.has_corridor_levy_charges}, Invoice: {container.c_sales_invoice}")
2025-06-05 22:14:29,810 INFO ipython print("\n=== Final Gate Pass Validation ===")
2025-06-05 22:14:29,810 INFO ipython print("1. Storage charges pending:", container.days_to_be_billed > 0)
2025-06-05 22:14:29,810 INFO ipython print("2. Removal charges pending:", container.has_removal_charges == "Yes" and not container.r_sales_invoice)
2025-06-05 22:14:29,811 INFO ipython print("3. Corridor charges pending:", container.has_corridor_levy_charges == "Yes" and not container.c_sales_invoice)
2025-06-05 22:14:29,811 INFO ipython any_pending = (container.days_to_be_billed > 0 or 
               (container.has_removal_charges == "Yes" and not container.r_sales_invoice) or
                              (container.has_corridor_levy_charges == "Yes" and not container.c_sales_invoice))
2025-06-05 22:14:29,811 INFO ipython print(f"\nAny pending payments: {any_pending}")
2025-06-05 22:14:29,811 INFO ipython print("🎉 Gate Pass should be allowed!" if not any_pending else "❌ Gate Pass will be blocked!")
2025-06-05 22:14:29,811 INFO ipython === session end ===
2025-06-19 17:42:19,491 INFO ipython === bench console session ===
2025-06-19 17:42:19,493 INFO ipython import frappe
2025-06-19 17:42:19,493 INFO ipython # Create a test employee
2025-06-19 17:42:19,493 INFO ipython test_employee = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Test Employee for Deletion",
            "first_name": "Test",
                "last_name": "Employee",
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active"
                                })
2025-06-19 17:42:19,493 INFO ipython test_employee.insert()
2025-06-19 17:42:19,494 INFO ipython print(f"Created test employee: {test_employee.name} - {test_employee.employee_name}")
2025-06-19 17:42:19,494 INFO ipython # Test deletion immediately
2025-06-19 17:42:19,494 INFO ipython try:
        frappe.delete_doc("Employee", test_employee.name)
            print(f"✅ SUCCESS: Employee {test_employee.name} deleted successfully!")
2025-06-19 17:42:19,494 INFO ipython except Exception as e:
        print(f"❌ ERROR: Failed to delete employee: {e}")
2025-06-19 17:42:19,494 INFO ipython frappe.db.commit()
2025-06-19 17:42:19,494 INFO ipython # Get a department first
2025-06-19 17:42:19,494 INFO ipython departments = frappe.get_all("Department", limit=1)
2025-06-19 17:42:19,495 INFO ipython if departments:
        dept = departments[0].name
        else:
                # Create a test department
2025-06-19 17:42:19,495 INFO ipython     test_dept = frappe.get_doc({
        "doctype": "Department",
            "department_name": "Test Department"
            })
2025-06-19 17:42:19,495 INFO ipython     test_dept.insert()
2025-06-19 17:42:19,495 INFO ipython     dept = test_dept.name
2025-06-19 17:42:19,495 INFO ipython print(f"Using department: {dept}")
2025-06-19 17:42:19,495 INFO ipython # Create a test employee with all required fields
2025-06-19 17:42:19,495 INFO ipython test_employee = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Test Employee for Deletion",
            "first_name": "Test",
                "last_name": "Employee", 
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active",
                                    "department": dept,
                                        "national_identity": "123456789"
                                        })
2025-06-19 17:42:19,495 INFO ipython test_employee.insert()
2025-06-19 17:42:19,496 INFO ipython print(f"Created test employee: {test_employee.name} - {test_employee.employee_name}")
2025-06-19 17:42:19,496 INFO ipython # Test deletion
2025-06-19 17:42:19,496 INFO ipython try:
        frappe.delete_doc("Employee", test_employee.name)
            print(f"✅ SUCCESS: Employee {test_employee.name} deleted successfully!")
2025-06-19 17:42:19,496 INFO ipython except Exception as e:
        print(f"❌ ERROR: Failed to delete employee: {e}")
2025-06-19 17:42:19,497 INFO ipython frappe.db.commit()
2025-06-19 17:42:19,497 INFO ipython frappe.delete_doc("Employee", "HR-EMP-00028")
2025-06-19 17:42:19,497 INFO ipython print("✅ SUCCESS: Employee HR-EMP-00028 deleted successfully!")
2025-06-19 17:42:19,497 INFO ipython === session end ===
2025-06-19 17:50:25,977 INFO ipython === bench console session ===
2025-06-19 17:50:25,978 INFO ipython import frappe
2025-06-19 17:50:25,978 INFO ipython # Create a test employee
2025-06-19 17:50:25,978 INFO ipython test_employee = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Test Employee Final",
            "first_name": "Test",
                "last_name": "Final",
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active",
                                    "department": "Test Department - A",
                                        "national_identity": "987654321"
                                        })
2025-06-19 17:50:25,978 INFO ipython test_employee.insert()
2025-06-19 17:50:25,978 INFO ipython print(f"Created test employee: {test_employee.name} - {test_employee.employee_name}")
2025-06-19 17:50:25,978 INFO ipython # Test deletion
2025-06-19 17:50:25,979 INFO ipython frappe.delete_doc("Employee", test_employee.name)
2025-06-19 17:50:25,979 INFO ipython print(f"✅ SUCCESS: Employee {test_employee.name} deleted successfully!")
2025-06-19 17:50:25,979 INFO ipython frappe.db.commit()
2025-06-19 17:50:25,979 INFO ipython # Test force deletion (bypasses link checking)
2025-06-19 17:50:25,979 INFO ipython test_employee2 = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Test Employee Force",
            "first_name": "Test",
                "last_name": "Force",
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active",
                                    "department": "Test Department - A",
                                        "national_identity": "111222333"
                                        })
2025-06-19 17:50:25,979 INFO ipython test_employee2.insert()
2025-06-19 17:50:25,979 INFO ipython print(f"Created test employee: {test_employee2.name} - {test_employee2.employee_name}")
2025-06-19 17:50:25,979 INFO ipython # Force delete (bypasses link checking)
2025-06-19 17:50:25,979 INFO ipython frappe.delete_doc("Employee", test_employee2.name, force=True)
2025-06-19 17:50:25,980 INFO ipython print(f"✅ SUCCESS: Employee {test_employee2.name} force deleted successfully!")
2025-06-19 17:50:25,980 INFO ipython frappe.db.commit()
2025-06-19 17:50:25,980 INFO ipython === session end ===
2025-06-19 18:02:39,571 INFO ipython === bench console session ===
2025-06-19 18:02:39,572 INFO ipython import frappe
2025-06-19 18:02:39,572 INFO ipython # Create a final test employee
2025-06-19 18:02:39,572 INFO ipython test_employee = frappe.get_doc({
    "doctype": "Employee",
        "employee_name": "Final Test Employee",
            "first_name": "Final",
                "last_name": "Test",
                    "gender": "Male",
                        "date_of_birth": "1990-01-01",
                            "date_of_joining": "2024-01-01",
                                "status": "Active",
                                    "department": "Test Department - A",
                                        "national_identity": "999888777"
                                        })
2025-06-19 18:02:39,572 INFO ipython test_employee.insert()
2025-06-19 18:02:39,573 INFO ipython print(f"Created test employee: {test_employee.name} - {test_employee.employee_name}")
2025-06-19 18:02:39,573 INFO ipython # Test normal deletion (should work without errors now)
2025-06-19 18:02:39,573 INFO ipython frappe.delete_doc("Employee", test_employee.name)
2025-06-19 18:02:39,573 INFO ipython print(f"🎉 SUCCESS: Employee {test_employee.name} deleted successfully WITHOUT ERRORS!")
2025-06-19 18:02:39,573 INFO ipython frappe.db.commit()
2025-06-19 18:02:39,573 INFO ipython === session end ===
2025-07-03 15:00:40,818 INFO ipython === bench console session ===
2025-07-03 15:00:40,820 INFO ipython # First, let's check if there are any employees
2025-07-03 15:00:40,820 INFO ipython employees = frappe.get_all("Employee", fields=["name", "employee_name"], limit=5)
2025-07-03 15:00:40,820 INFO ipython print("Available employees:")
2025-07-03 15:00:40,820 INFO ipython for emp in employees:
        print(f"- {emp.name}: {emp.employee_name}")
        
2025-07-03 15:00:40,820 INFO ipython # Let's test the current code with ignore_permissions=True
2025-07-03 15:00:40,820 INFO ipython # First, let's create a test Employee Advance
2025-07-03 15:00:40,821 INFO ipython test_employee = "HR-EMP-00005"  # Issa Selemani Msongola
2025-07-03 15:00:40,821 INFO ipython # Create a test Employee Advance
2025-07-03 15:00:40,821 INFO ipython advance = frappe.new_doc("Employee Advance")
2025-07-03 15:00:40,821 INFO ipython advance.employee = test_employee
2025-07-03 15:00:40,821 INFO ipython advance.purpose = "Test Travel Advance"
2025-07-03 15:00:40,821 INFO ipython advance.advance_amount = 50000
2025-07-03 15:00:40,821 INFO ipython advance.travel_request_ref = "TEST-TR-001"  # This is required for the hook to trigger
2025-07-03 15:00:40,821 INFO ipython advance.company = "Clouds Entertainment Co Ltd"
2025-07-03 15:00:40,821 INFO ipython advance.advance_account = "Employee Advances - CE"
2025-07-03 15:00:40,821 INFO ipython advance.insert()
2025-07-03 15:00:40,821 INFO ipython print(f"Created Employee Advance: {advance.name}")
2025-07-03 15:00:40,822 INFO ipython # Let's check what Travel Requests exist
2025-07-03 15:00:40,822 INFO ipython travel_requests = frappe.get_all("Travel Request", fields=["name", "purpose"], limit=5)
2025-07-03 15:00:40,822 INFO ipython print("Available Travel Requests:")
2025-07-03 15:00:40,822 INFO ipython for tr in travel_requests:
        print(f"- {tr.name}: {tr.purpose}")
        # Let's check what Travel Requests exist with basic fields
        travel_requests = frappe.get_all("Travel Request", fields=["name"], limit=5)
        print("Available Travel Requests:")
        for tr in travel_requests:
                print(f"- {tr.name}")
                
2025-07-03 15:00:40,822 INFO ipython # If there are any, let's use the first one
2025-07-03 15:00:40,822 INFO ipython if travel_requests:
        valid_tr = travel_requests[0].name
            print(f"Using Travel Request: {valid_tr}")
2025-07-03 15:00:40,822 INFO ipython else:
        print("No Travel Requests found")
2025-07-03 15:00:40,822 INFO ipython # Let's test the function directly without creating Employee Advance
2025-07-03 15:00:40,822 INFO ipython # First, let's import the function
2025-07-03 15:00:40,822 INFO ipython from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
2025-07-03 15:00:40,822 INFO ipython # Let's check if there are any existing Employee Advances with travel_request_ref
2025-07-03 15:00:40,822 INFO ipython existing_advances = frappe.get_all("Employee Advance", 
                                 filters={"travel_request_ref": ["!=", ""]}, 
                                                                  fields=["name", "travel_request_ref"], 
                                                                                                   limit=3)
2025-07-03 15:00:40,823 INFO ipython print("Existing Employee Advances with travel_request_ref:")
2025-07-03 15:00:40,823 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site rubis console
2025-07-03 15:00:40,823 INFO ipython === session end ===
2025-07-06 17:58:13,327 INFO ipython === bench console session ===
2025-07-06 17:58:13,328 INFO ipython === session end ===
2025-07-06 18:00:45,241 INFO ipython === bench console session ===
2025-07-06 18:00:45,241 INFO ipython === session end ===
2025-07-06 18:01:02,543 INFO ipython === bench console session ===
2025-07-06 18:01:02,544 INFO ipython import frappe
2025-07-06 18:01:02,544 INFO ipython # Check if the patch has already been executed
2025-07-06 18:01:02,544 INFO ipython patches = frappe.db.sql("SELECT patch FROM `tabPatch Log` WHERE patch='frappe.patches.v16_0.enable_setup_complete'", as_dict=True)
2025-07-06 18:01:02,544 INFO ipython print("Patch already executed:", len(patches) > 0)
2025-07-06 18:01:02,544 INFO ipython # Manually mark the patch as completed if it's stuck
2025-07-06 18:01:02,544 INFO ipython if len(patches) == 0:
    frappe.db.sql("INSERT INTO `tabPatch Log` (patch) VALUES ('frappe.patches.v16_0.enable_setup_complete')")
    frappe.db.commit()
    print("Patch marked as completed")
else:
    print("Patch was already in the log")
    
2025-07-06 18:01:02,544 INFO ipython === session end ===
2025-07-06 18:01:16,343 INFO ipython === bench console session ===
2025-07-06 18:01:16,343 INFO ipython import frappe
2025-07-06 18:01:16,344 INFO ipython from frappe.utils import now_datetime
2025-07-06 18:01:16,344 INFO ipython # Check if the patch has already been executed
2025-07-06 18:01:16,344 INFO ipython patches = frappe.db.sql("SELECT patch FROM `tabPatch Log` WHERE patch='frappe.patches.v16_0.enable_setup_complete'", as_dict=True)
2025-07-06 18:01:16,344 INFO ipython print("Patch already executed:", len(patches) > 0)
2025-07-06 18:01:16,344 INFO ipython # Manually mark the patch as completed if it's stuck
2025-07-06 18:01:16,344 INFO ipython if len(patches) == 0:
    patch_name = frappe.generate_hash(length=10)
    frappe.db.sql("""
        INSERT INTO `tabPatch Log` (name, owner, creation, modified, modified_by, patch) 
        VALUES (%s, 'Administrator', %s, %s, 'Administrator', 'frappe.patches.v16_0.enable_setup_complete')
    """, (patch_name, now_datetime(), now_datetime()))
    frappe.db.commit()
    print("Patch marked as completed")
else:
    print("Patch was already in the log")
2025-07-06 18:01:16,344 INFO ipython # Also ensure setup_complete is set
2025-07-06 18:01:16,344 INFO ipython frappe.db.set_single_value('System Settings', 'setup_complete', 1)
2025-07-06 18:01:16,345 INFO ipython frappe.db.commit()
2025-07-06 18:01:16,345 INFO ipython print("Setup complete flag set")
2025-07-06 18:01:16,345 INFO ipython === session end ===
2025-07-06 18:05:08,694 INFO ipython === bench console session ===
2025-07-06 18:05:08,694 INFO ipython import frappe
2025-07-06 18:05:08,694 INFO ipython # Check which doctype is causing the issue
2025-07-06 18:05:08,695 INFO ipython print("Checking installed apps...")
2025-07-06 18:05:08,695 INFO ipython apps = frappe.get_installed_apps()
2025-07-06 18:05:08,696 INFO ipython print("Installed apps:", apps)
2025-07-06 18:05:08,696 INFO ipython # Try to identify which doctype might be stuck
2025-07-06 18:05:08,696 INFO ipython frappe.db.sql("SHOW PROCESSLIST")
2025-07-06 18:05:08,697 INFO ipython === session end ===
2025-07-06 18:06:29,755 INFO ipython === bench console session ===
2025-07-06 18:06:29,755 INFO ipython import frappe
2025-07-06 18:06:29,755 INFO ipython from frappe.model.sync import sync_for
2025-07-06 18:06:29,755 INFO ipython # Try to sync each app individually to see which one is causing issues
2025-07-06 18:06:29,756 INFO ipython apps = frappe.get_installed_apps()
2025-07-06 18:06:29,756 INFO ipython for app in apps:
    try:
        print(f"Syncing {app}...")
        sync_for(app, force=0, reset_permissions=False)
        print(f"{app} synced successfully")
    except Exception as e:
        print(f"Error syncing {app}: {str(e)[:200]}")
        break
2025-07-06 18:06:29,756 INFO ipython === session end ===
2025-07-07 22:36:17,928 INFO ipython === bench console session ===
2025-07-07 22:36:17,928 INFO ipython frappe.db.exists("DocType", "Bank Reconciliation Filter")
2025-07-07 22:36:17,928 INFO ipython === session end ===
2025-07-07 22:40:16,726 INFO ipython === bench console session ===
2025-07-07 22:40:16,726 INFO ipython from csf_tz.csftz_hooks.enhanced_bank_reconciliation import EnhancedMatchingEngine
2025-07-07 22:40:16,727 INFO ipython print("Enhanced Bank Reconciliation module loaded successfully!")
2025-07-07 22:40:16,727 INFO ipython === session end ===
